<script setup>
import { mdiPlus, mdiSendClock } from '@mdi/js';
import QuotationPage from './QuotationPage.vue';
import SavedQuotationsDataProvider from '../components/SavedQuotationsDataProvider.vue';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { QuotationStatus } from '@/util/enums';

</script>
<template>
  <QuotationPage :headers="{head: ['Quotation Number',  'Accepted date'], row: ['quotationCode', 'acceptedDate']}" :status="QuotationStatus.ACCEPTED" :DataProvider="SavedQuotationsDataProvider" class="flex flex-col" :icon="mdiSendClock">
    <template #actions="{row}">
      <DropdownBtn @click="$router.push('/view_quotation/' + row?.institutionUuid + '/' + row?.quotationUuid)">
        View Quotation
      </DropdownBtn>
      <!-- <DropdownBtn @click="$router.push('/quotation_sample/' + row?.institutionUuid + '/' + row?.quotationUuid)">
        Policy Debit Note
      </DropdownBtn> -->
    </template>
  </QuotationPage>
</template>