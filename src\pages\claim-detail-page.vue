<script setup>
import { onMounted, ref, computed } from 'vue';
import * as session from '@/scripts/session';
import { useRouter, useRoute } from 'vue-router';
import SectionMain from '@/components/section/SectionMain.vue';
import api from '@/scripts/api';
import Modal from '@/components/modal.vue';
import { Icon } from '@iconify/vue';
import { Field, Form, useForm } from 'vee-validate';
import { useToast } from 'vue-toastification';
import { formatCurrency } from '@/util/utils'
import loader from '@/components/loader/loader.vue';
import ConfirmModal from '@/components/ConfirmModal.vue';
import serviceList from '@/components/claims/serviceList.vue';
import timeline from '@/components/timeline/timeline.vue';
import attachments from '@/components/attachments/attachments.vue';
import customInput from '@/components/forms/custom/input.vue';

const emit = defineEmits(['claim-checked']);

const route = useRoute();
const router = useRouter();
const { handleSubmit } = useForm({});
const toast = useToast();

const rejectItem = ref();
const openRejectClaim = ref(false);
const loading = ref(false);
const confirmModal = ref(false);
const approveModal = ref(false);
const approveAuthModal = ref(false);
const openCheckNumber = ref(false);
const openRemark = ref(false);
const description = ref('');
const buttonTitle = ref('');
const claim = ref([]);
const selectedServices = ref([]);
const claimCheckLoading = ref(false);
const claimUuid = ref(route.params.id);
const checkNumber = ref('');

const user = session.getUser();

const currentRole = computed(() => {
    if (user?.personalInfos.role && user?.personalInfos.role == 'ROLE_Finance') {
        return 'Finance';
    } else if (
        user?.personalInfos.role &&
        user?.personalInfos.role == 'PayerClaimReviewer'
    ) {
        return 'Reviewer';
    } else if (
        user?.personalInfos.role &&
        user?.personalInfos.role == 'PayerClaimAuditor'
    ) {
        return 'Auditor';
    } else if (
        user?.personalInfos.role &&
        user?.personalInfos.role == 'PayerClaimApprover'
    ) {
        return 'Approver';
    } else if (
        user?.personalInfos.role &&
        user?.personalInfos.role == 'Superman'
    ) {
        return 'Admin';
    }
});

user?.personalInfos;

const getClaimDetail = async () => {
    loading.value = true;
    try {
        await api.makeAuthenticatedRequest({
            method: 'Get',
            url: `/claim/details/${route.params.id}`,
        }).then((data) => {
            claim.value = data;
            console.log(data);
            loading.value = false;
        });
    } catch (error) {
        toast.error(error.message);
    }
}

function handleModalState() {
    openRejectClaim.value = false;
}
const rejectDescription = ref();
const submit = handleSubmit(async (values) => {
    confirmModal.value = true;
    rejectItem.value = `claimUuid=${route.params.id}&comment=${values['Description']}`;
});

const handleClaimChecked = async () => {
    claimCheckLoading.value = true;
    const role = currentRole.value;

    if ((role === 'Reviewer' || role === 'Admin') && route.params.status === 'Requested') {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/api/payer/claimconnect/claim/approve/processedBy`,
            data: {
                comment: description.value,
                batchCode: route.params.Uuid,
                claimUuidRequest: [
                    {
                        claimUuid: claimUuid.value
                    }
                ]
            }
        }).then((data) => {
            toast.success(data.message);
            emit('claim-checked');
            router.back();
            claimCheckLoading.value = false;
        });
    } else if ((role === 'Checker' || role === 'Admin') && route.params.status === 'Processed') {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/claim/approve/checkedBy`,
            data: {
                comment: description.value,
                batchCode: route.params.Uuid,
                claimUuidRequest: [
                    {
                        claimUuid: claimUuid.value
                    }
                ]
            }
        }).then((data) => {
            toast.success(data.message);
            emit('claim-checked');
            router.back();
            claimCheckLoading.value = false;
        });
    } else if ((role === 'Approver' || role === 'Admin') && route.params.status === 'Checked') {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/claim/approve/approvedBy`,
            data: {
                comment: description.value,
                batchCode: route.params.Uuid,
                claimUuidRequest: [
                    {
                        claimUuid: claimUuid.value
                    }
                ]
            }
        }).then((data) => {
            toast.success(data.message);
            emit('claim-checked');
            router.push();
            claimCheckLoading.value = false;
        });
    } else if ((role === 'Authorizer' || role === 'Admin') && route.params.status === 'Approved') {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/claim/approve/authorizedBy`,
            data: {
                comment: description.value,
                batchCode: route.params.Uuid,
                claimUuidRequest: [
                    {
                        claimUuid: claimUuid.value
                    }
                ]
            }
        }).then((data) => {
            toast.success(data.message);
            emit('claim-checked');
            router.back();
            claimCheckLoading.value = false;
        });
    } else if ((role === 'Finance' || role === 'Admin') && route.params.status === 'Authorized') {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/claim/settle/payment`,
            data: [
                {
                    claimUuid: claimUuid.value,
                    providerUuid: user?.providerUuid,
                    checkNumber: checkNumber.value,
                    transactionNumber: '',
                    paidByFullName: user?.firstName + ' ' + user?.fatherName
                }
            ]
        }).then((data) => {
            toast.success(data.message);
            emit('claim-checked');
            router.back();
            claimCheckLoading.value = false;
        });
    }
};

async function confirm() {
    await api.makeAuthenticatedRequest({
        method: 'Put',
        url: `/claim/deny/payer?${rejectItem.value}`,
    }).then((data) => {
        loading.value = false;
        toast.success(data.message);
        openRejectClaim.value = false;
        emit('claim-checked');
        router.push('/claims');
        confirmModal.value = false;
    });
}

const claimProcess = () => {
    if (route.params.status == 'Authorized') {
        openCheckNumber.value = !openCheckNumber.value
    } else {
        openRemark.value = !openRemark.value;
    }
}

const updateSelectedServices = (services) => {
    selectedServices.value = services
}

const addRemark = () => {
    if (route.params.status === 'Authorization') {
        approveAuthModal.value = true;
    } else {
        approveModal.value = true;
    }
}

const approveAuthorization = () => {
    openRemark.value = !openRemark.value;
}

async function confirmApproval() {
    approveAuthModal.value = false;
    handleAuthorization();
}

async function confirmApprove() {
    approveModal.value = false;
    handleClaimChecked();
}

const handleAuthorization = async () => {
    loading.value = true
    try {
        await api.makeAuthenticatedRequest({
            method: 'Put',
            url: `/claim-authorization/approve`,
            data: {
                claimAuthorizationUuid: route.params.caId,
                payerComment: description.value,
                status: 'Requested',
                services: selectedServices.value
            }
        }).then((data) => {
            loading.value = false
            toast.success(data.message);
            router.back();
        });
    } catch (error) {
        loading.value = false
        toast.error(error.message)
    }
}

onMounted(() => {
    getClaimDetail()
    if (route.params.status == 'Requested') {
        buttonTitle.value = 'Process'
    } else if (route.params.status == 'Processed') {
        buttonTitle.value = 'Verify'
    } else if (route.params.status == 'Checked') {
        buttonTitle.value = 'Approve'
    } else if (route.params.status == 'Approved') {
        buttonTitle.value = 'Authorize'
    } else if (route.params.status == 'Authorized') {
        buttonTitle.value = 'Pay'
    } else {
        buttonTitle.value = ''
    }
})
</script>
<template>
    <div class="w-full h-max">
        <!-- Reject Claim -->
        <ConfirmModal v-model="confirmModal" @confirm="confirm" icon="ion:warning-outline" title="Reject Claim"
            description="Are you sure you want to reject this claim?" confirmButton="Reject"
            iconClass="text-red-600 p-1 text-3xl" iconWrapperClass="bg-red-100 rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-600 sm:ml-3 sm:w-auto" />
        <ConfirmModal v-model="approveModal" @confirm="confirmApprove" icon="simple-line-icons:check"
            :title="buttonTitle + ' ' + `Claim`"
            :description="'Are you sure you want to ' + buttonTitle + ' this claim?'" :confirmButton="buttonTitle"
            iconClass="text-primary p-1 text-3xl" iconWrapperClass="bg-primary rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />
        <ConfirmModal v-model="approveAuthModal" @confirm="confirmApproval" icon="simple-line-icons:check"
            title="Approve Authorization" :description="'Are you sure you want to Authorize this Request?'"
            confirmButton="Authorize" iconClass="text-primary p-1 text-3xl"
            iconWrapperClass="bg-primary rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />
        <Modal :open="openRejectClaim" @close="handleModalState" title="Reject Claim" :autoClose="true">
            <div class="flex flex-col pb-5">
                <form class="mt-5 mb-5 flex flex-col" @submit.prevent="submit">
                    <h3 class="flex gap-x-1 font-semibold text-gray-600 mb-3">
                        Reason <span class="text-red-600">*</span>
                    </h3>
                    <Field v-slot="{ field, errors }" v-model="rejectDescription" name="Description" rules="required">
                        <textarea v-bind="field" name="Description" class="rounded-lg"
                            placeholder="State your reason here . . ." rows="7" cols="30" :class="errors[0]
                                ? 'ring-1 ring-red-600 focus:ring-1 focus:ring-red-600 border-red-600 focus:border-red-600 focus:outline-none'
                                : ''
                                " />
                        <small v-if="errors[0]" class="text-red-600 font-light mt-1">{{
                            errors[0]
                            }}</small>
                    </Field>
                    <div class="flex justify-end mt-5">
                        <button
                            class="text-lg px-7 py-2 font-semibold text-white bg-primary0 rounded-lg hover:shadow-lg duration-200 hover:bg-primary">
                            Submit
                        </button>
                    </div>
                </form>
            </div>
        </Modal>
        <Modal :open="openRemark" @close="openRemark = false" title="Add Remark" :autoClose="true">
            <div class="flex flex-col w-80 pb-5 mt-8">
                <textarea rows="4" v-model="description" name="description" id="description" placeholder="Remark ..."
                    class="block w-full h-20 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                <div class="flex justify-end mt-5">
                    <button
                        class="text-lg px-4 py-2 font-semibold text-white bg-primary0 rounded-lg hover:shadow-lg duration-200 hover:bg-primary"
                        @click="addRemark">
                        Add
                    </button>
                </div>

            </div>
        </Modal>
        <SectionMain>
            <div class="min-h-screen flex flex-col gap-y-5">
                <div v-if="loading" class="flex items-center justify-center h-[40vh]">
                    <loader />
                </div>
                <div v-else>
                    <div class="flex text-sm justify-between gap-x-10 bg-white sticky top-0 backdrop-blur-xl py-5 z-50">
                        <div class="flex items-center gap-x-5 text-black text-2xl">
                            <div class="bg-white p-3 rounded-full">
                                <Icon icon="fluent:document-person-16-filled" class="text-3xl" />
                            </div>
                            Claim Detail
                        </div>
                        <div class="flex gap-x-10 self-center"
                            v-if="claim?.payerStatus != 'Pending' && claim?.claimStatus !== null && route.params.status !== 'Authorized' && route.params.status !== 'Paid' && route.params.status !== 'Authorization'">
                            <button @click="openRejectClaim = true"
                                class="px-6 py-2 rounded-md bg-red-500 hover:bg-red-600 duration-200 text-white font-medium hover:shadow-lg">
                                Reject Claim
                            </button>
                            <button @click="claimProcess"
                                class="px-6 py-2 rounded-md bg-primary hover:bg-primary duration-200 text-white font-medium hover:shadow-lg">
                                {{ buttonTitle }}
                            </button>
                        </div>
                        <div class="flex gap-x-10 self-center" v-else-if="route.params.status === 'Authorization'">
                            <button @click="openRejectClaim = true"
                                class="px-6 py-2 rounded-md bg-red-500 hover:bg-red-600 duration-200 text-white font-medium hover:shadow-lg">
                                Decline
                            </button>
                            <button @click="approveAuthorization"
                                class="px-6 py-2 rounded-md bg-primary hover:bg-primary duration-200 text-white font-medium hover:shadow-lg">
                                Authorize Request
                            </button>
                        </div>
                    </div>

                    <div class="flex gap-x-14">
                        <div class="w-[75%] bg-white rounded-xl py-5 px-10" v-if="claim.dependantUuid == null">
                            <h3 class="text-lg font-medium text-gray-600 mb-5">
                                Policy Holder Info
                            </h3>
                            <div class="flex flex-col gap-y-5">
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Full Name</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.firstName }} {{ claim.fatherName }} {{ claim.grandFatherName }}
                                        </p>
                                    </div>
                                    <div class="flex-1 flex gap-x-10 p-2">
                                        <p class="text-gray-500 w-[8rem]">Provider Name</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.providerName }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex gap-x-10 h-full">
                                        <p class="text-gray-500 w-[8rem]">Policy Number</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.insuranceId }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Phone Number</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.providerPhone }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap"
                                    v-if="claim?.processorComment != null">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Processor Comment</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.processorComment }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap"
                                    v-if="claim?.checkerComment !== null">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Checker Comment</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.checkerComment }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap"
                                    v-if="claim?.approverComment !== null">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Approver Comment</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.approverComment }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap"
                                    v-if="claim?.authorizerComment !== null">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Authorizer Comment</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.authorizerComment }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-[75%] bg-white rounded-xl py-5 px-10" v-else>
                            <h3 class="text-lg font-medium text-gray-600 mb-5">
                                Policy Holder Info({{ claim.firstName }} {{ claim.fatherName }})
                            </h3>
                            <div class="flex flex-col gap-y-5">
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Full Name</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.dependantFirstName }} {{ claim.dependantFatherName }} {{
                                                claim.dependantGrandFatherName }}
                                        </p>
                                    </div>
                                    <div class="flex-1 flex gap-x-10 p-2">
                                        <p class="text-gray-500 w-[8rem]">Provider Name</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.providerName }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex gap-x-10 h-full">
                                        <p class="text-gray-500 w-[8rem]">Policy Number</p>
                                        <p class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 shadow-inner">
                                            {{ claim.insuranceId }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-x-10 justify-between flex-wrap">
                                    <div class="flex-1 flex">
                                        <p class="text-gray-500 mr-10 w-[8rem]">Relation Ship</p>
                                        <p
                                            class="flex-1 bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all shadow-inner h-full">
                                            {{ claim.relationship }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl px-10 py-5 w-[35%] flex flex-col gap-y-5">
                            <h3>Group Info</h3>
                            <div class="flex justify-between">
                                <p class="text-gray-500">Group Name</p>
                                <p class="bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-words w-[14rem]">
                                    {{ claim.institutionName }}
                                </p>
                            </div>
                            <div class="flex justify-between">
                                <p class="text-gray-500">Group Policy Number</p>
                                <p
                                    class="bg-gray-100 px-5 py-1 rounded-md text-gray-600 break-all w-[14rem] flex items-center">
                                    {{ claim.payerInstitutionContractCode }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col overflow-x-scroll gap-y-5 px-10 py-5 rounded-lg bg-white">
                        <h3 class="text-2xl text-gray-600">Services</h3>
                        <serviceList :claimUuid="route.params.id" @selected-services="updateSelectedServices" />
                    </div>
                    <div class="flex flex-col gap-y-5 px-10 py-10 rounded-lg pb-5 bg-white">
                        <h2 class="text-2xl text-gray-600 font-semibold">More Info</h2>
                        <div class="flex justify-between">
                            <div class="flex-1 flex flex-col gap-y-5 py-6 px-4 max-h-[250px] overflow-scroll">
                                <timeline :claimUuid="route.params.id" />
                            </div>
                            <div class="flex-1 flex flex-col gap-y-2.5 items-center">
                                <h3 class="text-lg font-semibold text-gray-600 mb-5">
                                    Attachments
                                </h3>
                                <div class="px-16 py-3 max-h-[250px] overflow-scroll">
                                    <attachments :claimUuid="route.params.id" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SectionMain>
    </div>
</template>
