<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import { Input, Select } from '@/components/form_elements'
import FormLayout from '@/components/FormLayout.vue'
import FormSubmitButton from '@/components/FormSubmitButton.vue'
import { mdiClose } from '@mdi/js';
import { ref } from 'vue';

const props = defineProps({
  showBtn: {
    type: Boolean,
    default: true
  },
  pending: {
    type: Boolean,
    default: false
  },
  institution: {
    type: Object
  },
  btnText: {
    type: String,
    required: true
  },
  onSubmit: {
    type: Function,
    required: true
  }
})

const category = ref('')
function submitForm({ values }) {
  let telephone = values.telephone.startsWith('+251') ? values.telephone : '+251' + values.telephone.slice(0)
  props.onSubmit({ ...values, status: 'ACTIVE', institutionInsuranceNumber: 'this is a dummy data' })
}

const institutionTypes = [
  'School',
  'College',
  'University',
  'Hospital',
  'Clinic',
  'Nursing Home',
  'Nonprofit Organization',
  'Museum',
  'Library',
  'Art Gallery',
  'Theater',
  'Bank',
  'Insurance Company',
  'other'
]

const sourceOfBusiness = ['Broker', 'Direct', 'Agent', 'Branch']
const source = ref('Direct')
</script>

<template>
  <FormLayout v-slot="{ submit }">
    <Input name="institutionName" placeholder="name" label="Institution Name" validation="required"
      :value="institution?.['institutionName'] || ''" />
    <Input name="email" placeholder="email" label="Email" validation="required|email"
      :value="institution?.['email'] || ''" />
    <Input name="tinNumber" placeholder="tinNumber" label="Tin Number" validation="num"
      :value="institution?.['tinNumber'] || ''" />
    <Input name="description" placeholder="description" label="Description" validation="required"
      :value="institution?.['description'] || ''" />
    <Input name="telephone" placeholder="telephone" label="Telephone" validation="required|phone"
      :value="institution?.['telephone'] || ''" />
    <Select v-if="category != 'other'" v-model="category" name="category" placeholder="Category" label="category"
      :value="institution?.['category'] || ''" :options="institutionTypes" />
    <div v-else class="relative">
      <div @click="category = ''" class="absolute w-8 h-8 z-20 top-8 right-0 rounded-2xl">
        <BaseIcon :path="mdiClose" :size="15" />
      </div>
      <Input validation="required" name="category" placeholder="Put In Other" label="category"
        :value="institution?.['category'] || ''" />
    </div>
    <div class="col-span-2 grid grid-cols-5 gap-2">
      <Input name="address1" placeholder="woreda" label="Woreda" validation="required"
        :value="institution?.['address1'] || ''" />
      <Input name="address2" placeholder="sub city" label="Sub City" validation="required"
        :value="institution?.['address2'] || ''" />
      <Input name="address3" placeholder="city" label="City" validation="required"
        :value="institution?.['address3'] || ''" />
      <Select name="state" class="" label="State" :value="institution?.['state'] || ''" :options="[
        'Addis Ababa',
        'Tigray',
        'Afar',
        'Amahara',
        'Oromia',
        'Somali',
        'Benishangul Gumz',
        'Centeral Ethiopia',
        'Gambela',
        'Harari',
        'Sidama',
        'South West Ethiopia',
        'South Ethiopia',
        'Dire Dawa',
      ]" validation="required" />
      <Input :disabled="true" name="country" label="Country" value="Ethiopia" />
    </div>
    <Select
      v-model="source"
      name="sourceOfBusiness"
      label="Source Of Business"
      :value="institution?.['sourceOfBusiness']"
      :options="sourceOfBusiness"
    />
    <Input
      v-if="source != 'Direct'"
      name="referredBy"
      validation="required"
      :label="`${source} Name`"
      placeholder="Name"
      :value="institution?.['referredBy'] || ''"
    />
    <FormSubmitButton
      v-if="showBtn"
      @click.prevent="submit(submitForm)"
      :pending="pending"
      class="col-span-2"
      :btn-text="btnText"
    />
  </FormLayout>
</template>