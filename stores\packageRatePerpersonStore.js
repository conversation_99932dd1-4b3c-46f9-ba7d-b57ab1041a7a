
import packageRatePerpersonService from "@/services/packageRatePerpersonService";
import { defineStore } from "pinia";
export const useRatePerpersonStore=defineStore('rateperpersonstore',
  {


    state: () => ({
        packageRateList: [],
        id: 0,
        loading:false
      }),


// action functions
      actions: {
        async  getAllPackagesRatePerperson() {
         
          this.packageRateList=await packageRatePerpersonService.getPackageRatePerperson()
          this.loading=true
          
           },


        async addPackageRatePerperson(data) {
        
         this.packageRateList=await packageRatePerpersonService.addPackageRatePerperson(data)
          
           },
  //  update package status
        async updatePackagePerpersonStatus(data,id){
          try {
              
           console.log('data',data,id) 
          
            this.pending=true  
            const d=await packageRatePerpersonService.updatePackageStatus(data,id)
            this.pending=false
          return d;
          
        } catch (error) {
          this.pending=false
          
        }

            },

           async updatePackagePerperson(data,id){
            try {
                 
            
              this.pending=true  
              const d=await packageRatePerpersonService.updatePackage(data,id)
              this.packageRateList=d
              this.pending=false
             return d;
             
           } catch (error) {
             this.pending=false
            
           }
    
               },

           async deletePackagePerperson(id)
           {
                try {
                 let response=await packageRatePerpersonService.deletePackagePerpersonById(id)
                    if(response) {
                      return true
                    }
                    else {
                      return false
                    }
                 
                } catch (error) {
                   return false
                }
           },
          async getPackageById(id) {

             this.loading=true
             let data=await packageRatePerpersonService.getPackageById(id) 
            
              const formattedData = data.map(item => {
                return {
                    ...item,
                    maxLimit: item.maxLimit.toLocaleString('en-US'),
                    minLimit: item.minLimit.toLocaleString('en-US')
                };
            });


             return formattedData
           
          },
          async addPackageRange(data) {
            this.PackageRange.push(data)
          },

          async deletePackageRange(id) {
            this.PackageRange.splice(id,1)
            
          },


      }


  })