<script setup>
  import { getAllInstitutions } from '@/features/quotation/api/institutionApi'
  import { Status } from '@/util/enums';
  import SearchWithPagination from '@/components/SearchWithPagination.vue'

  const props = defineProps({
    status: {
      type: String,
      default: Status.ACTIVE
    },
    search: String
  })

</script>

<template>
  <SearchWithPagination :search="search" v-slot="{ data, pending }" :cb="(data, config) => getAllInstitutions({...data, status: props.status}, config)">
    <slot 
      :institutions="data"
      :pending="pending"
    />
  </SearchWithPagination>
</template>