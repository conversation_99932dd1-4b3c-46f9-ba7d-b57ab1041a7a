import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/util/utils";

const path = "/quotation";

export async function createQuotation(data, config = {}) {
  const api = new ApiService();
  return await api.addAuthenticationHeader().post(path, data, config);
}

export async function updateQuotation(id, data, config = {}) {
  const api = new ApiService();
  return await api.addAuthenticationHeader().put(`${path}/${id}`, data, config);
}

export async function issueQuotation(id, data, config = {}) {
  console.log(data);
  const api = new ApiService();
  return await api
    .addAuthenticationHeader()
    .put(`${path}/issue?quotationUuid=${id || ""}`, data, config);
}

export async function acceptQuotation(id, data, config = {}) {
  console.log(data);
  const api = new ApiService();
  return await api
    .addAuthenticationHeader()
    .put(`${path}/accept/${id}`, data, config);
}

export async function getQuotations(query = {}, config = {}) {
  const api = new ApiService();
  const qs = getQueryFormObject(query);
  return await api.addAuthenticationHeader().get(`${path}${qs}`, config);
}

export async function getQuotationById(id, config = {}) {
  const api = new ApiService();
  return await api.addAuthenticationHeader().get(`${path}/${id}`, config);
}

export async function payQuotation(id, data, config = {}) {
  const api = new ApiService();
  const qs = getQueryFormObject(data)
  return api.addAuthenticationHeader().put(`${path}/pay/${id}${qs}`, data, config);
}

export async function replicateQuotation(id, config = {}) {
  const api = new ApiService();
  return api
    .addAuthenticationHeader()
    .post(`${path}/replicate/${id}`, {}, config);
}

export async function issuePremiumAdvice(id, config = {}) {
  const api = new ApiService();
  return api
    .addAuthenticationHeader()
    .put(`${path}/issue-premium-advice/${id}`, {}, config);
}
