<script setup>
import * as session from '@/scripts/session';
import { useRouter } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import { mdiOfficeBuilding, mdiPlus, mdiBadgeAccount } from '@mdi/js';
import SvgIcon from '@jamescoyle/vue-icon';
import { onMounted, ref } from 'vue';
import Modal from '@/components/modal.vue';
import InstitutionRegistrationForm from '@/components/forms/InstitutionRegistration.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import DeleteInstitution from '@/components/actions/deleteInstitution.vue';
import ClaimListTabs from '@/components/claims/tabs/claimListTabs.vue';

import ClaimsList from '@/components/claims/claimsList/claimsList.vue';
import loader from '@/components/loader/loader.vue';

const open = ref(false);
const loading = ref(false);
</script>
<template>
  <div class="w-full h-max">
    <Modal :open="open" @close="handleModalClose('Create')" title="Institution Information">
    </Modal>
      <SectionMain>
        <SectionTitleLineWithButton :icon="mdiBadgeAccount" title="Claims" main>
        </SectionTitleLineWithButton>
        <div class="min-h-[70vh]">
          <div v-if="loading" class="flex items-center justify-center h-[40vh]">
            <loader />
          </div>

          <ClaimsList v-if="!loading" @delete="(id) => CatchInstitutionToBeDeleted(id)" />
        </div>
      </SectionMain>
  </div>
</template>
