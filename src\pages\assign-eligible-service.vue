<script setup>
import { useRouter, useRoute } from 'vue-router';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import BaseButton from '@/components/base/BaseButton.vue';
import { mdiHandExtendedOutline, mdiPlus, mdiMenu, mdiNumeric9PlusCircleOutline } from '@mdi/js';
import Modal from '@/components/modal.vue';
import api from '@/scripts/api';
import loader from '@/components/loader/loader.vue';
import { Icon } from '@iconify/vue';
import ConfirmModal from '@/components/ConfirmModal.vue';
import { Form, useForm } from 'vee-validate';
import { useToast } from 'vue-toastification';
import { ref, onMounted } from 'vue';
import SvgIcon from '@jamescoyle/vue-icon';
import EditGroupService from '@/components/forms/EditGroupService.vue';
import customInput from '@/components/forms/custom/input.vue';
import familyPlans from '@/scripts/plans';

const toast = useToast();
const { handleSubmit } = useForm({});
const loading = ref(false);
const selectedService = ref();
const servicesLoading = ref(false);
const approveModal = ref(false);
const route = useRoute();
const dropdownIndex = ref(null)
const openForm = ref(false);
const services = ref([]);
const myService = ref([]);
const eligibleServices = ref();
const ServiceGroup = ref('');
const openAddEligibleService = ref(false);
const maxBenefitForEmployee = ref();
const maxBenefitForSpouse = ref();
const maxBenefitForChildren = ref();
const maxAllowedDependants = ref();
const maxAllowedDependantAge = ref();
const plans = ref([]);
const planType = ref('');
const familyPoolBenefit = ref();
const institutionName = ref('');
const excessAllowed = ref(false);
const Authorization = ref(false);

function handleEligibleServiceModalState() {
  openAddEligibleService.value = false;
}

const displayMenu = (index) => {
  dropdownIndex.value = dropdownIndex.value === index ? null : index;
}

const openServiceModal = (service) => {
  openForm.value = !openForm.value
  myService.value = service
  dropdownIndex.value = null
}

const getInstitution = async () => {
  loading.value = true;
  await api.get(`/institution/${route.params.Uuid}`).then((data) => {
    institutionName.value = data.institutionName;
    loading.value = false;
  });
};

const fetchEligibleServices = async () => {
  loading.value = true;
  await api.get(`/insured-service/contract/${route.params.id}`).then((data) => {
    eligibleServices.value = data;
    loading.value = false;
  });
};

const fetchAllServices = async () => {
  servicesLoading.value = true;
  await api.get(`/package/active/list`).then((data) => {
    services.value = data;
    servicesLoading.value = false;
  });
};

const submit = handleSubmit(async (values) => {
  selectedService.value = {
    payerInstitutionContractUuid: route.params.id,
    packageUuid: ServiceGroup.value,
    maxBenefitForEmployee: maxBenefitForEmployee.value,
    maxBenefitForSpouse: maxBenefitForSpouse.value,
    maxBenefitForChildren: maxBenefitForChildren.value,
    familyPoolBenefit: familyPoolBenefit.value,
    maxAllowedDependants: maxAllowedDependants.value,
    maxAllowedDependantAge: maxAllowedDependantAge.value,
    planType: planType.value,
    excessAllowed: excessAllowed.value,
    authorizationRequired: Authorization.value,
    status: "ACTIVE"
  };
  approveModal.value = true;
});

const closeModal = () => {
  openForm.value = !openForm.value;
  fetchEligibleServices();
}

async function confirmApprove() {
  servicesLoading.value = true;
  approveModal.value = false;
  await api.post(`/insured-service`, selectedService.value).then((data) => {
    servicesLoading.value = false;
    approveModal.value = false;
    toast.success(data.message);
    handleEligibleServiceModalState();
    fetchEligibleServices();
  });
}

const handleServiceModal = () => {
  openAddEligibleService.value = !openAddEligibleService.value;
  maxBenefitForEmployee.value = '';
  maxBenefitForSpouse.value = '';
  maxBenefitForChildren.value = '';
  maxAllowedDependants.value = '';
  maxAllowedDependantAge.value = '';
  fetchAllServices();
}

onMounted(() => {
  plans.value = familyPlans;
  getInstitution();
  fetchEligibleServices();
});
</script>

<template>
  <div class="mt-5">
    <ConfirmModal v-model="approveModal" @confirm="confirmApprove" icon="simple-line-icons:check"
      title="Benefit Category" description="Are you sure you want to add this Benefit Category?" confirmButton="Save"
      iconClass="text-primary p-1 text-3xl" iconWrapperClass="bg-primary rounded-full p-1"
      confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />

    <Modal :open="openForm" @close="closeModal" :icon="mdiNumeric9PlusCircleOutline" className="h-9 w-9"
      title="Service Detail" class="pt-10">
      <EditGroupService @save="closeModal" :services=myService />
    </Modal>
    <SectionMain class="">
      <SectionTitleLineWithButton :icon="mdiHandExtendedOutline" :title="institutionName + ' ' + 'Product Coverages'"
        main>
        <div class="flex space-x-2">
          <form class="w-full mb-5" v-if="!loading">
            <label for="default-search"
              class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
            <div class="relative">
              <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                <Icon icon="mdi:magnify" class="text-2xl" />
              </div>
              <input type="search" id="default-search"
                class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search . . ." />
              <button type="submit"
                class="text-white absolute end-2.5 bottom-2.5 bg-primary0 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Search
              </button>
            </div>
          </form>
          <div class="mt-2">
            <BaseButton :icon="mdiPlus" color="whiteDark" @click="handleServiceModal" label="Add Coverage" />
          </div>
        </div>
      </SectionTitleLineWithButton>
      <Modal :open="openAddEligibleService" @close="handleEligibleServiceModalState" title="Add Benefit Category"
        :autoClose="true">
        <div class="flex flex-col">
          <form class="mt-5 mb-5 w-[30rem] flex flex-col" @submit.prevent="submit">
            <h3 class="flex gap-x-1 font-semibold text-gray-600 mb-3">
              Category <span class="text-red-600">*</span>
            </h3>
            <select v-model="ServiceGroup"
              class="block w-60 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:leading-6">
              <option v-for="service in services" :key="service.packageUuid" :value="service.packageUuid">
                {{ service.packageName }}
              </option>
            </select>

            <div v-if="ServiceGroup">
              <div class="mt-2">
                <label for="selectPlan">Select Service Plan <span class="text-red-600">*</span></label>
                <select v-model="planType"
                  class="block w-60 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:leading-6">
                  <option v-for="plan in plans" :key="plan.name" :value="plan.name">
                    {{ plan.name }}
                  </option>
                </select>
              </div>
              <customInput type="number" label="" placeholder="Max. Allowed Dependent" v-model="maxAllowedDependants"
                :required="true" />
              <customInput type="number" label="" placeholder="Max. Allowed Dependents Age"
                v-model="maxAllowedDependantAge" :required="true" />
              <div class="mt-2" v-if="planType == 'Individual_Plan'">
                <customInput type="number" label="" placeholder="employee benefit" v-model="maxBenefitForEmployee"
                  :required="true" />
                <customInput type="number" label="" placeholder="spouse benefit" v-model="maxBenefitForSpouse"
                  :required="true" />
                <customInput type="number" label="" placeholder="children benefit" v-model="maxBenefitForChildren"
                  :required="true" />
                <label for="" class="p-2">Is Excess Allowed</label>
                <input type="checkbox" placeholder="Is Excess Allowed" v-model="excessAllowed" class="m-2" />
                <label for="" class="p-2">Authorization Required</label>
                <input type="checkbox" placeholder="" v-model="Authorization" class="m-2" />
              </div>
              <div class="mt-2" v-else-if="planType == 'Family_Plan'">
                <customInput type="number" label="" placeholder="family benefit" v-model="familyPoolBenefit"
                  :required="true" />
                <label for="" class="p-2">Is Excess Allowed</label>
                <input type="checkbox" placeholder="Is Excess Allowed" v-model="excessAllowed" class="m-2" />
                <label for="" class="p-2">Authorization Required</label>
                <input type="checkbox" placeholder="" v-model="Authorization" class="m-2" />
              </div>
              <div v-else>
                <customInput type="number" label="" placeholder="employee benefit" v-model="maxBenefitForEmployee"
                  :required="true" />
                <customInput type="number" label="" placeholder="spouse benefit" v-model="maxBenefitForSpouse"
                  :required="true" />
                <customInput type="number" label="" placeholder="children benefit" v-model="maxBenefitForChildren"
                  :required="true" />
                <label for="" class="p-2">Is Excess Allowed</label>
                <input type="checkbox" placeholder="Is Excess Allowed" v-model="excessAllowed" class="m-2" />
                <label for="" class="p-2">Authorization Required</label>
                <input type="checkbox" placeholder="" v-model="Authorization" class="m-2" />
              </div>
            </div>

            <div class="flex justify-end mt-5">
              <button
                class="flex items-center text-lg px-4 py-1 font-semibold text-white bg-primary0 rounded-lg hover:shadow-lg duration-200 hover:bg-primary">
                <Icon icon="lucide:check" class="text-2xl" />
                Save
              </button>
            </div>
          </form>
        </div>
      </Modal>

      <div class="min-h-[70vh]">
        <div v-if="loading" class="flex items-center justify-center h-[40vh]">
          <loader />
        </div>
        <div class="flow-root" v-if="!loading">
          <div class="overflow-x-auto">
            <div
              class="ring-1 ring-black ring-opacity-5 sm:rounded-lg max-w-[80vw] max-h[80vh] xl:max-h-[80vh] overflow-auto">
              <table
                class="min-w-full overflow-hidden ring-1 ring-black ring-opacity-5 sm:rounded-lg max-h[15rem] xl:max-h-[20rem] overflow-y-auto">
                <thead class="relative top-0 rounded-t-lg">
                  <tr class="px-3 divide-gray-200 bg-gray-200">
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"></th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      <span>Service Group</span>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      <span>Description</span>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Family Benefit
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Employee Benefit
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Spouse Benefit
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Children Benefit
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-for="(service, index) in eligibleServices" :key="service.serviceListUuid"
                    class="divide-gray-200 px-3 hover:bg-grey-200 duration-200 items-start w-full even:bg-gray-50">
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-center font-medium text-gray-900">
                      {{ index + 1 }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 font-medium text-gray-900">
                      {{ service?.item }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.description }}
                    </td>

                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.familyPoolBenefit }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.maxBenefitForEmployee }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.maxBenefitForSpouse }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.maxBenefitForChildren }}
                    </td>

                    <td class="whitespace-nowrap py-2 px-2 text-primary font-semibold underline underline-offset-4">
                      <div class="relative inline-block text-left">
                        <button type="button" @click="displayMenu(index)"
                          class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-indigo-500"
                          id="options-menu" aria-haspopup="true">
                          <svg-icon type="mdi" :path="mdiMenu"></svg-icon>
                        </button>

                        <div v-if="dropdownIndex === index"
                          class="origin-top-right top-[120%] absolute right-0 mt-2 mb-2 w-40 rounded-md shadow-lg bg-gray-100 ring-1 ring-black ring-opacity-5 z-10">
                          <div class="py-1" role="menu" aria-orientation="horizontal" aria-labelledby="options-menu">
                            <span @click.prevent="openServiceModal(service)"
                              class="block px-4 py-2 text-sm cursor-pointer hover:bg-white hover:text-gray-900"
                              role="menuitem">View</span>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>
