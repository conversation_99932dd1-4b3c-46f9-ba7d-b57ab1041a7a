<script setup>
import TableWrapper from '@/components/TableWrapper.vue';
import AuthorizedClaimsDataProvider from '../components/AuthorizedClaimsDataProvider.vue';
import { ClaimStatus } from '@/util/enums';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { secondDateFormat } from '@/util/utils';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { mdiBadgeAccount } from '@mdi/js';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { openModal } from '@/modals';
</script>
<template>
  <div class="w-full h-max">
    <SectionMain>
      <SectionTitleLineWithButton :icon="mdiBadgeAccount" title="Claims" main>
      </SectionTitleLineWithButton>

      <AuthorizedClaimsDataProvider :status="ClaimStatus.AUTHORIZED" v-slot="{ claims, pending }">
        <TableWrapper
          :pending="pending"
          :Fallback="TableRowSkeleton"
          :defaultAction="false"
          :headers="{
            head: ['Policy Holder Name', 'Provider Name', 'Batch Code', 'Requested Date', 'Status', 'modify'],
            row: ['name', 'pName', 'batchCode', 'authDate', 'claimStatus']
          }"
          :rows="claims.reduce((state, el) => {
            el.name = `${el.institutionName} (${el.institutionPhone})`
            el.pName = `${el.providerName} (${el.providerPhone})`
            el.authDate = secondDateFormat(el.authorizedDate)
            state.push(el)
            return state
          }, [])"
        >
          <template #actions="{ row }">
            <DropdownBtn
              @click.prevent="$router.push({
                name: 'claim-batch-details',
                params: {
                  id: row?.batchCode,
                  Uuid: row?.providerUuid,
                  status: row?.claimStatus
                },
              })"
            >
              Details
            </DropdownBtn>  
            <DropdownBtn
              @click.prevent="$router.push({
                name: 'CPO',
                params: {
                    id: row?.batchCode,
                    Uuid: row?.providerUuid,
                    status: row?.claimStatus
                },
              })"
            >
              View CPO
            </DropdownBtn>  
          </template>
          <template #additionalBtns="{ row }">
            <button @click="openModal('PayClaim', {
              batchCode: row.batchCode,
              providerUuid: row.providerUuid,
              amount: row.totalAmount 
            })" class="bg-lime-500 text-white w-12 h-10 rounded-md">
              Pay
            </button>
          </template>
        </TableWrapper>
      </AuthorizedClaimsDataProvider>
    
    </SectionMain>
  </div>
</template>