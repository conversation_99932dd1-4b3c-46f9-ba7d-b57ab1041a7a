<script setup>
  import TableWrapper from '@/components/TableWrapper.vue';
  import { secondDateFormat } from '@/util/utils';
  import { openModal } from '@customizer/modal-x'
  import { ref } from 'vue';
  import DependantForm from './form/DependantForm.vue'
  const props = defineProps({
    dependants: Array
  })

  const dependant = ref()

  const emit = defineEmits(['action:edit', 'ction:delete'])
</script>
<template>
  <div v-if="dependants.length" class="flex flex-col gap-2">
    <p class="text-lg font-semibold border-y py-1">Dependants</p>
    <TableWrapper
      @action:edit="(row) => dependant = row"
      @action:delete="(row) => emit('action:delete', row)"
      :headers="{
        head: ['full name', 'relationship', 'phone', 'birthDate', 'modify'],
        row: ['fullname', 'relationship', 'phone', 'dob'],
      }"
      :rows="dependants.reduce((state, el) => {
        el.fullname = `${el.title ? el.title : ''} ${el.firstName} ${el.fatherName} ${el.grandFatherName}`
        el.dob = secondDateFormat(el.birthDate)
        state.push(el)
        return state
      }, [])"
    >
    <div class="fixed inset-0 z-[504] overflow-y-scroll flex p-3 justify-center bg-gray-500/75" v-if="dependant">
      <FormParent class="w-[50rem] bg-white rounded-md" title="Add Dependants">
        <DependantForm
          btn-text="Edit Dependant"
          :on-submit="() => {}"
          :dependant="dependant"
        >
        </DependantForm>
      </FormParent>
    </div>
    </TableWrapper>
  </div>  
</template>