<script setup>
import { mdiMenu<PERSON><PERSON>, mdiGreater<PERSON>han, mdiChevronRight } from '@mdi/js';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import menuAsideData from '@/menuAside.js';
import menuNavBar from '@/menuNavBar.js';
import { useDarkModeStore } from '../../stores/darkMode';
import BaseIcon from '@/components/base/BaseIcon.vue';
import NavBar from '@/components/nav-bar/NavBar.vue';
import * as session from '@/scripts/session';
import { screens } from 'tailwindcss/defaultTheme'
import AsideMenu from '@/components/aside-menu/AsideMenu.vue';
import FooterBar from '@/components/FooterBar.vue';

const menuAside = menuAsideData();

const darkModeStore = useDarkModeStore();

const router = useRouter();

const isAsideMobileExpanded = ref(false);
const isAsideLgActive = ref(false);

router.beforeEach(() => {
  isAsideMobileExpanded.value = false;
  isAsideLgActive.value = false;
});

const menuClick = (event, item) => {
  if (item.isToggleLightDark) {
    darkModeStore.set();
  }

  if (item.isLogout) {
    session.logoutUser();
    router.push('/login');
  }
};

const open = ref(true)

onMounted(() => {
  const observer = new ResizeObserver((entries) => {
    entries.forEach(entry => {
      if (entry.contentRect.width < parseInt(screens.md)) {
        open.value = false
      } else {
        open.value = true
      }
    })
  })

  observer.observe(document.body)
})
</script>

<template>
  <div class="flex w-full h-screen bg-section-bg">
    <div :class="[open ? 'min-w-[20rem] w-80' : 'min-w-[8rem] w-32 overflow-hidden']"
      class="hidden md:block bg-white overflow-hidden transition-all duration-200 ease-linear h-full relative left-0 top-0 z-30">
      <AsideMenu :menu="menuAside" :open="open" />
      <button :class="[!open ? 'rotate-180' : '']" @click="open = !open"
        class="origin-center transition-transform duration-300 ease-linear absolute top-16 left-8 z-50 h-[16px] w-12">
        <BaseIcon :size="28" :path="mdiChevronRight"></BaseIcon>
      </button>
    </div>

    <div @click="open = false" :class="[open ? 'translate-x-0' : '-translate-x-full']"
      class="md:hidden z-50 -translate-x-full transition-all duration-200 ease-linear fixed top-0 left-0 bg-black/50 w-full h-full">
      <div @click.stop="() => { }" class="w-80 bg-slate-50 overflow-hidden h-full relative">
        <AsideMenu :menu="menuAside" :open="true" />
        <button :class="[!open ? 'rotate-180' : '']" @click="open = !open"
          class="origin-center transition-transform duration-300 ease-linear absolute top-0 right-0 z-50 h-16 w-12 bg-slate-50">
          <BaseIcon :size="25" :path="mdiMenuOpen"></BaseIcon>
        </button>
      </div>
    </div>

    <button :class="[!open ? 'rotate-180' : '']" @click="open = !open"
      class="grid place-items-center md:hidden absolute top-0 left-0 origin-center transition-transform duration-300 ease-linear z-0 h-16 w-12 bg-white">
      <BaseIcon :size="25" :path="mdiMenuOpen"></BaseIcon>
    </button>

    <div style="width: calc(100% - 18rem)" class="flex-1 mx-4 flex flex-col">
      <div class="h-16 mt-2">
        <slot name="navbar">
          <NavBar :menu="menuNavBar" @menu-click="menuClick">
          </NavBar>
        </slot>
      </div>
      <div style="min-height: calc(100% - 4rem);" class="overflow-scroll bg-bg-clr py-4">
        <RouterView />
        <FooterBar> </FooterBar>
      </div>
    </div>
  </div>
</template>
