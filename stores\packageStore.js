import packageService from "@/services/packageService";
import { set } from "date-fns";
import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
const toast = useToast();
export const usePackageStore=defineStore('packageStore',
  {
        state: () => ({
        packageList: [],
        PackageRange:[],
        selectedCoverages:[],
        pending:false
      }),

      actions: {
        async  getAllPackages() {

          this.pending=true
         this.packageList=await packageService.getAllPackages()
        //  this.pending=false
         setTimeout(() => {
          this.pending=false
      }, 2000)
        
       
       },

           async  addSelectedPackage(data) {
            try {
             
                this.selectedCoverages=data

              
            } catch (error) {
                         
            }
           },


        async  addPackage(data) {
            try {
             
                this.pending=true  
                const d=await packageService.addPackage(data)
                this.packageList.unshift(d)
                 this.pending=false
                  return true
              
            } catch (error) {
              this.pending=false
             
            
      
              
            }
           },

      async updatePackage(data,id){
        try {
             
          this.pending=true  
          const d=await packageService.updatePackage(data,id)
          let index = this.packageList.findIndex(p => p.packageUuid === id);
            if(index) {
              this.packageList[index]=d
            }    
          this.pending=false
         return true
         
       } catch (error) {
         this.pending=false
        
       }

           },


        async deletePackage(packageUuid)
          {
              try {
                await packageService.deletePackageId(packageUuid)
                this.packageList.splice(packageUuid,1)
             
                  return true


                
              } catch (error) {
                console.log('eror',error)
                
              }
             
          },
          async getPackageById(id) {

           this.loading=true
            let data=await packageService.getPackageById(id)
               if(data)
                {
                 return data

                }
           
          },
          async addPackageRange(data) {
            this.PackageRange.push(data)
          },

          async deletePackageRange(id) {
            this.PackageRange.splice(id,1)
            
          },


      }

      


  })