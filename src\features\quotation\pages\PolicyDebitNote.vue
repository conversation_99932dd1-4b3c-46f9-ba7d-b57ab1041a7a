<script setup>
import PolicyDebitNoteDataProvider from '@/features/quotation/components/PolicyDebitNoteDataProvider.vue'
import PolicyDebitNotePdf from '@/features/quotation/components/PolicyDebitNote.pdf.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import Loader from '@/components/loader/loader.vue';

</script>
<template>
  <SectionMain>
    <PolicyDebitNoteDataProvider v-slot="{ pending, ...rest }">
      <PolicyDebitNotePdf v-bind="rest" v-if="!pending" />
      <div v-else class="h-[20rem] grid place-items-center">
        <Loader />
      </div>
      </PolicyDebitNoteDataProvider>
  </SectionMain>
</template>