<script setup>
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import { ref, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia'
import { usePackageStore } from '../../stores/packageStore'
import { useRatePerpersonStore } from '../../stores/packageRatePerpersonStore'
import { useRatePerFamiliyStore } from '../../stores/packageRatePerfamiliyStore'
import Modal from "@/components/modal.vue";
import EditPackageStatus from '@/components/forms/EditPackageStatus.vue';
import DeleteConfirmtionRate from '@/components/forms/DeleteConfirmtionRate.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const packageStore = usePackageStore()
const ratePerpersonStore = useRatePerpersonStore()
const ratePerfamiliyStore = useRatePerFamiliyStore()
const props = defineProps({ id: { type: Number } })
const packageName = ref(route.query.packageName)
const packageUuid = ref(route.params.id);
const { packageList, PackageRange } = storeToRefs(packageStore);
const open = ref(false)
const packageRangeId = ref('')
const familiyPerInput = ref([])
let temPackages = ref([])
const perPersonInput = ref([{ 'packageUuid': packageUuid.value, 'minLimit': '', 'maxLimit': '', 'rate': '', 'status': 'ACTIVE' }])
const stype = ref('')
const benefitType = ref('')
const item = ref({})
const openDeleteModal = ref(false)
const openModal = (sitem, types) => {

  open.value = !open.value;
  stype.value = types;
  console.log('type', stype.value)
  item.value = sitem;
  console.log('item', item.value)
};

const openConfirmModal = (id, bType) => {

  console.log('iddddd', id)
  openDeleteModal.value = true
  benefitType.value = bType
  packageRangeId.value = id

}
const DeleteRanges = () => {
  if (benefitType.value == 'indiv') {

    deletePerperson(packageRangeId.value)

  }
  else {
    deletePackagePerFamily(packageRangeId.value)
  }


}


const closeDeleteModal = () => {

  openDeleteModal.value = false
}


const saveToDBPerPerson = async () => {

  console.log('you called me')
  const formattedData = perPersonInput.value.map(item => ({
    ...item,
    maxLimit: item.maxLimit.replace(/,/g, ''),
    minLimit: item.minLimit.replace(/,/g, ''),


  }));

  console.log('formate data', formattedData);

  let data = await ratePerpersonStore.updatePackagePerperson(formattedData, packageUuid.value)

}
const saveToDBPerFamiliy = async () => {


  const formattedData = familiyPerInput.value.map(item => ({
    ...item,
    maxLimit: item.maxLimit.replace(/,/g, ''),
    minLimit: item.minLimit.replace(/,/g, ''),

  }));

  let data = await ratePerfamiliyStore.updatePackagePerfamiliy(formattedData, packageUuid)



}
const deletePerperson = async (id) => {
  let response = await ratePerpersonStore.deletePackagePerperson(id)

  if (response == true) {
    openDeleteModal.value = false
    perPersonInput.value = perPersonInput.value.filter(item => item.individualBenefitRangeUuid !== id);


  }
}


const deletePackagePerFamily = async (id) => {
  console.log('re dre ngh', id)

  let response = await ratePerfamiliyStore.deletePackagePerFamily(id)
  if (response == true) {
    openDeleteModal.value = false
    familiyPerInput.value = familiyPerInput.value.filter(item => item.familyBenefitRangeUuid !== id);


  }

}

const feachDataById = async () => {

  perPersonInput.value = await ratePerpersonStore.getPackageById(packageUuid.value)
  familiyPerInput.value = await ratePerfamiliyStore.getPackageById(packageUuid.value)
  console.log('famili per input', familiyPerInput.value)
}


onMounted(() => {
  feachDataById()

});

onMounted(() => {
  // This code will be executed after the component is mounted to the DOM
  console.log('Component is mounted!');

});


watch(PackageRange.value, () => {
  console.log('sssss', PackageRange.value)
  temPackages.value = PackageRange.value



})



</script>


<template>
  <div class="">
    <div>

      <Modal :open="open" @close="open = !open" title="Edit Status">
        <EditPackageStatus :item="item" :stype="stype" />
      </Modal>
      <Modal :open="openDeleteModal" @close="closeDeleteModal" title="Delete Coverage Range">
        <DeleteConfirmtionRate @confirmed="DeleteRanges" @canceled="closeDeleteModal"></DeleteConfirmtionRate>
      </Modal>
    </div>
    <div class="ml-[10px] min-h-[500px] mt-4">
      <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="flex justify-between items-center mb-4">
          <h1 class="text-2xl font-semibold">Setting About Coverage <span class="text-primary">{{ packageName }}</span></h1>
         
         
         
          <router-link to="/package">
            <a class="flex items-center text-primary hover:text-gray-700">
              <svg class="w-4 h-4 mr-2" xmlns="https://icons8.com/icon/63650/plus" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
              Back
            </a>
          </router-link>
        </div>



      </div>

      <div class="flex justify-evenly space-x-3">
        <!-- For Individual -->
        <div class="bg-gray-100 p-4 rounded-md">
          <div class="flex items-center justify-between mb-4">
            <p class="text-lg font-semibold">For Individual</p>

          </div>

          <table class="w-full">
            <!-- ... Your table header ... -->
            <thead class="bg-gray-50">

              <tr>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Min limit
                </th>

                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Max limit
                </th>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Rate(%)
                </th>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Status
                </th>
              </tr>
            </thead>

            <tbody class="">
              <tr v-for="(packageRange, index) in perPersonInput" :key="index">
                <!-- ... Your table row content ... -->
                <td class="border w-20"><input v-model="packageRange.minLimit"
                    class="w-[100px] h-2 m-0 border-white focus:border-white" type="text" id="name" /> </td>
                <td class="border w-20"><input v-model="packageRange.maxLimit" class="w-[100px] h-2 m-0 border-white"
                    type="text" id="name" /></td>
                <td class="border w-20"><input v-model="packageRange.rate"
                    class="w-[100px] h-2  border-white focus:border-white" type="text" id="name" /> </td>
                <td class="p-1"><span @click.prevent="openModal(packageRange, 'indiv')"
                    class="cursor-pointer p-1 text-xs font-medium  tracking-wider bg-green-200 rounded-lg bg-opacity-50"
                    :class="packageRange.status === 'ACTIVE' ? 'bg-green-100' : 'bg-yellow-100'">{{ packageRange.status
                    }}</span></td>

                <span> <a class="ml-1 font-medium text-red-500 text-center cursor-pointer"
                    @click.prevent="openConfirmModal(packageRange.individualBenefitRangeUuid, 'indiv')">
                    <icon icon="trash" class="text-center" />
                  </a></span>



              </tr>
            </tbody>

            <!-- ... Your table footer and buttons ... -->
          </table>

          <button @click="saveToDBPerPerson"
            class="mt-4 w-full bg-primary text-white rounded-md py-1 px-1 hover:bg-indigo-500">
            Update
          </button>
        </div>

        <!-- For Family -->
        <div class="bg-gray-100 p-4 rounded-md">

          <div class="flex items-center justify-between mb-4">
            <p class="text-lg font-semibold">For Family</p>

          </div>
          <!-- table -->

          <table class="w-full">
            <!-- ... Your table header ... -->
            <thead class="bg-gray-50">

              <tr>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Min limit
                </th>

                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Max limit
                </th>
                <th scope="col" class="border text-left text-sm text-gray-600 px-1 py-1 px-1 py-1">
                  Familiy Size
                </th>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Rate(%)
                </th>
                <th scope="col" class="border text-left text-sm  text-gray-600 px-1 py-1">
                  Status
                </th>
              </tr>
            </thead>


            <tbody>
              <tr v-for="(packageRange, index) in familiyPerInput">
                <td class="border w-20"><input v-model="packageRange.minLimit"
                    class="w-[100px] h-2 m-0 border-white focus:border-white" type="text" id="name" /> </td>
                <td class="border w-20"><input v-model="packageRange.maxLimit" class="w-[100px] h-2 m-0 border-white"
                    type="text" id="name" /></td>
                <td class="border w-20"><input v-model="packageRange.familySize"
                    class="w-[50px] h-2 m-0 border-white focus:border-white" type="text" id="name" /> </td>
                <td class="border w-20"><input @keypress="addFmiliyTablePressEnter(index)" v-model="packageRange.rate"
                    class="w-[70px] h-2 m-0 border-white focus:border-white" type="text" id="name" /> </td>
                <td class="p-1"><span @click.prevent="openModal(packageRange, 'famil')"
                    class="cursor-pointer p-1 text-xs font-medium  tracking-wider rounded-lg"
                    :class="packageRange.status === 'ACTIVE' ? 'bg-green-100' : (packageRange.status == 'SUSPENDED' ? 'bg-red-400' : 'bg-yellow-100')">{{
                      packageRange.status
                    }}</span>
                </td>
                <span> <a href="#" class="font-medium text-red-500"
                    @click.prevent="openConfirmModal(packageRange.familyBenefitRangeUuid, 'famil')">
                    <icon icon="trash" />

                  </a></span>

              </tr>
            </tbody>


            <!-- ... Your table footer and buttons ... -->
          </table>

          <button @click="saveToDBPerFamiliy"
            class="mt-4 w-full bg-primary text-white rounded-md py-1 px-1 hover:bg-indigo-500">
            Update
          </button>


        </div>
      </div>
    </div>
  </div>
</template>

