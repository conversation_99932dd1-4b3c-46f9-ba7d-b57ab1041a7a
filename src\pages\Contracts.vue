<script setup>
import { useRouter } from 'vue-router';
import SectionMain from '@/components/section/SectionMain.vue';
import api from '@/scripts/api';
import {
  mdiCheck,
  mdiHospitalBuilding,
  mdiCardAccountDetailsOutline,
  mdiTableEye,
  mdiReceiptTextOutline
} from '@mdi/js';
import { onMounted, ref, computed } from 'vue';
import SvgIcon from '@jamescoyle/vue-icon';
import Modal from '@/components/modal.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { useToast } from 'vue-toastification';
import AddContract from '../components/forms/AddPayerProviderContract.vue';
import { useRowStore } from '../../stores/threePageValue';
import BaseButton from '@/components/base/BaseButton.vue';
import { Icon } from '@iconify/vue';
import ConfirmModal from '@/components/ConfirmModal.vue';

const router = useRouter();
const toast = useToast();
const rowStore = useRowStore();

const approveModal = ref(false)
const openForm = ref(false);
const isLoading = ref(false);
const payerProviderContractUuid = ref('')
const services = ref([]);

const navigateToServicesPage = (contract) => {
  rowStore.setRowDetails(contract);
  router.push({
    name: 'contractServices',
    params: {
      providerId: contract.providerUuid,
      payerProviderContractUuid: contract.payerProviderContractUuid,
    },
  });
}

const navigateToInstitutionsPage = (contract) => {
  router.push({
    name: 'contractInstitutions',
    params: {
      payerProviderContractCode: contract.contractCode,
      payerProviderContractUuid: contract.payerProviderContractUuid,
    },
  })
}

const fetchContracts = async () => {
  isLoading.value = true
  try {
    await api.get('/payer-provider-contract/provider/lists?page=1&limit=25&status=PENDING').then((data) => {
      isLoading.value = false
      services.value = data
    })
  } catch (error) {
    isLoading.value = false
    toast.error(error)
  }
}

const deleteContract = async (contractUuid) => {
  isLoading.value = true;
  try {
    await api.delete(`/payer-provider-contract/${contractUuid}`).then((data) => {
      isLoading.value = false;
      toast.success(data.message);
    });
  } catch (error) {
    isLoading.value = false;
    toast.error(error);
  }
}

const approveContract = (contractUuid) => {
  payerProviderContractUuid.value = contractUuid
  approveModal.value = !approveModal.value;
}

const approveProviderContract = async (payerProviderContractUuid) => {
  isLoading.value = true;
  approveModal.value = !approveModal.value;
  try {
    await api.put(`/payer-provider-contract/approve/${payerProviderContractUuid}`).then((data) => {
      isLoading.value = false
      toast.success(data.message)
    })

  } catch (error) {
    isLoading.value = false
    toast.error(error.message)
  }
}

const handleModalState = () => {
  openForm.value = !openForm.value
  fetchContracts()
}

onMounted(() => {
  fetchContracts()
});
</script>

<template>
  <div class="w-full h-max">
    <ConfirmModal v-model="approveModal" @confirm="approveProviderContract(payerProviderContractUuid)"
      icon="simple-line-icons:check" title="Approve Contract"
      description="Are you sure you need to approve this contract? you can't undo it" confirmButton="Save"
      iconClass="text-primary p-1 text-3xl" iconWrapperClass="bg-primary rounded-full p-1"
      confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />

    <Modal :open="openForm" :icon="mdiReceiptTextOutline" title="Create Contract">
      <AddContract @close="handleModalState()" />
    </Modal>
    <SectionMain>
      <SectionTitleLineWithButton :icon="mdiCardAccountDetailsOutline" title="Provider Contracts" main class="provider">
        <form class="w-full md:w-[40%] lg:w-[30%] mb-2">
          <label for="default-search"
            class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
              <Icon icon="mdi:magnify" class="text-2xl" />
            </div>
            <input type="search" id="default-search"
              class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              placeholder="Search . . ." required />
            <button type="submit"
              class="text-white absolute end-2.5 bottom-2.5 bg-primary0 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              Search
            </button>
          </div>
        </form>
      </SectionTitleLineWithButton>
      <div class="mt-1 flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50 border-t-2 border-solid border-black">
                  <tr>
                    <th scope="col" class="py-3.5 pl-2 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                      S.N
                    </th>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                      @click="sort('contractName')">
                      Contract Name
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('payerName')">
                      Provider Name
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('contractCode')">
                      Contract Code
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('payerPhone')">
                      Provider Phone
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Effective Date
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('endDate')">
                      Status
                    </th>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-for="(contract, index) in services" :key="contract.payerProviderContractUuid"
                    class="h-2 box-content">
                    <td class="whitespace-wrap px-3 py-2 text-sm text-gray-500">
                      {{ index + 1 }}
                    </td>
                    <td class="whitespace-wrap py-2 px-3 text-sm font-medium text-gray-900">
                      {{ contract.contractName }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ contract.providerName }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ contract.contractCode }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ contract.providerPhone }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ contract.beginDate }} to {{ contract.endDate }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      <span v-if="contract.status === 'ACTIVE'
                      "
                        class="inline-flex items-center rounded-md bg-green-200 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                        {{ contract.status }}
                      </span>
                      <span v-if="contract.status === 'SUSPENDED'"
                        class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                        {{ contract.status }}
                      </span>
                    </td>
                    <td class="relative whitespace-nowrap py-2 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div class="flex gap-2 items-center justify-end">
                        <div
                          class="bg-primary round-sm flex gap-0.5 cursor-pointer w-25 item-center text-white px-2 py-1 rounded-lg"
                          @click="navigateToServicesPage(contract)">
                          <div class="text-white">
                            <svg-icon type="mdi" :path="mdiTableEye" class="w-4 h-4"></svg-icon>
                          </div>
                          <div class="font-small text-sm">Services</div>
                        </div>
                        <div
                          class="bg-green-600 rounded-s-sm flex gap-0.5 cursor-pointer item-center text-white px-1 py-1 rounded-lg"
                          @click="navigateToInstitutionsPage(contract)">
                          <div class="text-white">
                            <svg-icon type="mdi" :path="mdiHospitalBuilding" class="w-4 h-4"></svg-icon>
                          </div>
                          <div class="font-small text-sm">
                            Institutions
                          </div>
                        </div>
                        <BaseButton :icon="mdiCheck" label="" title="Approve Contract" color="red"
                          @click="approveContract(contract.payerProviderContractUuid)" />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <p v-if="services && services.length === 0"
                class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">
                No Contracts Were Found
              </p>
            </div>
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>

<style scoped></style>../stores/threePageValue