<script setup>
import { mdiPlus } from "@mdi/js";
import QuotationPage from "./QuotationPage.vue";
import InstitutionsDataProvider from "../components/InstitutionsDataProvider.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import DropdownBtn from "@/components/DropdownBtn.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
</script>
<template>
  <QuotationPage class="flex max-h-max flex-col" :icon="mdiPlus">
    <template #content="{ search }">
      <InstitutionsDataProvider
        :search="search"
        v-slot="{ institutions, pending, error }"
      >
        <TableWrapper
          :Fallback="TableRowSkeleton"
          :pending="pending"
          :defaultAction="false"
          :headers="[
            'institution Name',
            'address',
            'telephone',
            'status',
            'modify',
          ]"
          :rows="
            institutions?.reduce((state, el) => {
              el.address = `woreda ${el.address1}, ${el.address2}, ${el.address3}`;
              state.push(el);
              return state;
            }, [])
          "
        >
          <template #actions="{ row }">
            <DropdownBtn
              @click="$router.push('/create_quotation/' + row?.institutionUuid)"
            >
              New Quotation
            </DropdownBtn>
          </template>
        </TableWrapper>
        <div
          class="mt-2 shadow-md bg-white flex justify-center items-center h-24"
        >
          <button
            @click="$router.push('/create_quotation')"
            class="bg-white rounded-md px-4 py-1 text-primary border-2"
          >
            Add New Institution
          </button>
        </div>
      </InstitutionsDataProvider>
    </template>
  </QuotationPage>
</template>
