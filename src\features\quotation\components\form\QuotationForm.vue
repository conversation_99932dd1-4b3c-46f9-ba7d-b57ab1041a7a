<script setup>
import FormParent from '@/components/FormParent.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { mdiBullhorn, mdiClose, mdiMinusBox, mdiMinusCircle, mdiMinusThick, mdiOfficeBuilding, mdiPlus } from '@mdi/js';
import InstitutionForm from './InstitutionForm.vue';
import FormLayout from '@/components/FormLayout.vue';
import { formatCurrency, genId, addDate } from '@/util/utils'
import { computed, ref, watch } from 'vue';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { Input, Textarea } from '@/components/form_elements';
import Select from '@/components/form_elements/Select.vue';
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import { Status } from '@/util/enums';
import Denture from '../denture/Denture.vue';
import Eyeglass from '../eyeglass/Eyeglass.vue';
import Maternity from '../maternity/Maternity.vue';
import MaternityDelivery from '../maternity/MaternityDelivery.vue';
import PregnancyCheckup from '../maternity/PregnancyCheckup.vue';
import InOutPatient from '../basic/InOutPatient.vue';
import Inpatient from '../inpatient/Inpatient.vue';
import Outpatient from '@/features/quotation/components/outpatient/Outpatient.vue';
import Wellness from '../wellness/Wellness.vue';
import GeneralCheckup from '../general/GeneralCheckup.vue';
import { openModal } from '@/modals';
import MaternityQuote from '../MaternityQuote.vue';
import Quote from '@/features/quotation/components/Quote.vue';

const props = defineProps({
  formFilter: {
    type: Object
  },
  institution: {
    type: Object
  },
  quotation: {
    type: Object
  },
  actions: {
    type: Boolean,
    default: true
  },
  quotationTitle: String,
  institutionTitle: String,
  pending: {
    type: Boolean,
    default: false
  },
  packages: {
    type: Array,
    required: true
  },
  btnTextInstitution: {
    type: String,
    required: true
  },
  showIssueBtn: {
    type: Boolean,
    default: false
  },
  showQuotationBtn: {
    type: Boolean,
    default: true
  },
  showAcceptBtn: {
    type: Boolean,
    default: false
  },
  showInstitutionBtn: {
    type: Boolean,
    default: true
  },
  showProceedBtn: {
    type: Boolean,
    default: false
  },
  btnTextQuotation: {
    type: String,
    required: true
  },
  onSubmitInstitution: {
    type: Function,
    required: true
  },
  onSubmitQuotationIssue: {
    type: Function,
  },
  onSubmitQuotation: {
    type: Function,
    required: true
  },
  onSubmitProceedToMembers: {
    type: Function,
  },
  onSubmitAcceptQuotation: {
    type: Function,
  }
})

const femaleOnlyPackages = props.packages.map(el => { })
function submitInstitution(values) {
  props.onSubmitInstitution(values)
}

const packageNames = computed(() => props.packages?.map(el => el.packageName)?.toSorted())

const femaleOnlyPlan = ['Maternity Delivery', 'Pregnancy Checkup/ Followup', 'Pregnancy & Maternity']
const allMemberTYpes = [
  {
    label: 'Member Only',
    value: 1,
  },
  {
    label: 'Member + 1',
    value: 2,
  },
  {
    label: 'Member + 2',
    value: 3
  },
  {
    label: 'Member + 3',
    value: 4,
  },
  {
    label: 'Member + 4',
    value: 5
  },
  {
    label: 'Member + 5 or more',
    value: 6
  },
  {
    label: 'Spouse',
    value: 69,
  },
]
const plan = [
  {
    label: 'Individual Plan',
    value: 'Individual_Plan'
  },
  {
    label: 'Family Plan',
    value: 'Family_Plan'
  },
  {
    label: 'Family Shared Plan',
    value: 'Family_Shared_Plan'
  },
  // {
  //   label: 'Member Only',
  //   value: 'Member_Only'
  // }
]

const showSelectedPackageAddBtn = ref(false)
const addPackage = ref(false)
const selectedPackage = ref('')
const selectedPlan = ref('')

const quotations = ref({
  institutionUuid: '',
  description: '',
  quoatedServices: []
})

const femaleCoverage = props.packages.filter(el => el.gender == 'FEMALE').map(el => el.packageName)
const filteredplan = computed(() => {
  console.log(selectedPackage.value)
  if (femaleCoverage.includes(selectedPackage.value)) {
    return [{
      label: 'Individual Plan',
      value: 'Individual_Plan'
    }]
  }
  return plan
})

console.log(props.quotation)

const quotation = computed(() => props.quotation)

function reformatQuotation() {
  quotations.value.quotationUuid = props.quotation?.quotationUuid
  quotations.value.institutionUuid = props.institution?.institutionUuid
  quotations.value.description = props.quotation.description
  quotations.value.quoatedServices = props.quotation.quoatedServices?.reduce((state, el) => {
    let name = props.packages.find(pack => el.packageUuid == pack.packageUuid)?.packageName
    const packages = state.find(pack => pack.name == name)
    console.log(el)
    if (packages) {
      packages.services.push(el)
    } else {
      state.push({
        name,
        planType: el.planType || 'Family_Plan',
        services: [{
          ...el,
          id: genId.next().value,
          quotedServiceUuid: el.serviceQuotedUuid,
          package: name
        }]
      })
    }
    return state
  }, []) || []
}

watch(quotation, () => {
  reformatQuotation()
})

const familyTypes = computed(() => {
  return (name, id) => {
    const pack = quotations.value.quoatedServices.find(el => el.name == name)
    const selecteDescriptions = pack?.services.map(el => el.description) || []

    if (pack?.planType == 'Member_Only') {
      return allMemberTYpes.filter(el => el.value == 1)
    } else if (femaleOnlyPlan.includes(name)) {
      return allMemberTYpes.filter(el => [1, 69].includes(el.value) && !selecteDescriptions.find(sel => sel == el.value) || el.value == id)
    } else {
      return allMemberTYpes.filter(el => !selecteDescriptions.includes(el.value) || el.value == id)
    }
  }
})

if (props.quotation) {
  reformatQuotation()
}
// else {
//   addQuotation(packageNames.value?.[0], props.packages.find(el => el.packageName == packageNames.value?.[0])?.packageUuid)
// }

const exceptions = {
  'Inpatient': 'In/Out Patient',
  'Outpatient': 'In/Out Patient',
  'Maternity Delivery': 'Pregnancy & Maternity',
  'Pregnancy Checkup/ Followup': 'Pregnancy & Maternity',
  'Pregnancy & Maternity': ['Pregnancy Checkup/ Followup', 'Maternity Delivery']
}

function hasPackage(name) {
  if (Array.isArray(name)) {
    return quotations.value.quoatedServices.find(el => name.includes(el.name))
  } else {
    return quotations.value.quoatedServices.find(el => el.name == name)
  }
}

function addQuotation(packageName, planType) {
  if (!props.actions || !packageName) return

  const exception = exceptions[packageName]

  if (!hasPackage(exception)) {
    let idx = quotations.value.quoatedServices.findIndex(service => service.name == packageName)

    if (idx == -1) {
      quotations.value.quoatedServices.unshift({
        name: packageName,
        planType,
        services: [
          {
            id: genId.next().value,
            packageUuid: '',
            descriptio: '',
            coverage: '',
            individualType: 'NA',
            numberOfInsured: 0,
            premium: '',
            rate: '',
            package: packageName,
          }
        ]
      })
    }

    showSelectedPackageAddBtn.value = false
  } else {
    let str = ''
    if (Array.isArray(exception)) {
      str += exception.toString().replace(',', ' or ')
    } else {
      str = exception
    }
    openModal('Alert', {
      message: `Cant add [${packageName}] Because Coverage [${str}] Already Exists.`
    })
  }
}

function removePackage(name) {
  console.log(name)
  if (!props.actions) return
  quotations.value.quoatedServices = quotations.value.quoatedServices?.filter(el => el.name != name)
}

function remove(name, id) {
  if (!props.actions) return

  const idx = quotations.value.quoatedServices.findIndex(el => el.name == name)

  if (idx > -1 && quotations.value.quoatedServices[idx].services.length > 1) {
    const serviceIdx = quotations.value.quoatedServices[idx].services.findIndex(el => el.id == id)
    console.log(serviceIdx)
    if (serviceIdx > -1) {
      quotations.value.quoatedServices[idx].services.splice(serviceIdx, 1)
    }
  }
}

const rate = computed(() => (name, coverage, size) => {
  size = size == 69 ? 1 : size
  const packages = props.packages?.find(el => el.packageName == name)
  const limit = packages?.benefitRanges?.find(el => el.familySize == size && parseFloat(coverage) >= el.minLimit && parseFloat(coverage) <= el.maxLimit)
  console.log(limit)
  return limit?.rate || 0
})

const minmax = computed(() => {
  return (name) => {
    const pack = props.packages?.find(el => el.packageName == name)
    return { min: pack?.minLimit, max: pack?.maxLimit } || 0
  }
})

const totalSumInsured = computed(() => {
  return (name, id) => {
    const pack = quotations.value.quoatedServices.find(el => el.name == name)
    if (!pack) return 0
    const service = pack.services.find(el => el.id == id)
    return service.coverage * service.description * service.numberOfInsured
  }
})

const premium = computed(() => {
  return (name, id) => {
    const pack = quotations.value.quoatedServices.find(el => el.name == name)
    if (!pack) return 0
    const service = pack.services.find(el => el.id == id)
    if (!service) return 0
    const sum = service.numberOfInsured * service.coverage * rate.value(pack.name, service.coverage, service.description) / 100
    if (['Individual_Plan', 'Family_Shared_Plan'].includes(pack.planType)) {
      return sum * (service.description == 69 ? 1 : service.description)
    }
    return sum
  }
})

const subTotal = computed(() => {
  return (name) => {
    const pack = quotations.value.quoatedServices.find(el => el.name == name)
    if (!pack) return
    return pack.services
      .reduce((sum, el) => {
        return sum += premium.value(name, el.id)
      }, 0)
  }
})

const grandTotal = computed(() => {
  return quotations.value.quoatedServices
    .map(el => el.name)
    .reduce((sum, el) => {
      return sum += subTotal.value(el)
    }, 0)
})

const benefitTotal = computed(() => {
  return (name) => {
    const pack = quotations.value.quoatedServices
      .find(el => el.name == name)
    if (!name) return 0
    return pack.services.reduce((sum, el) => {
      return sum += totalSumInsured.value(name, el.id)
    }, 0)
  }
})

const grandBenefitTotal = computed(() => {
  return quotations.value.quoatedServices
    .map(el => el.name)
    .reduce((sum, el) => {
      return sum += benefitTotal.value(el)
    }, 0)
})

const totalBenefit = computed(() => {
  return quotations.value.quoatedServices
    .reduce((services, el) => {
      services.push(...el.services)
      return services
    }, [])
    .reduce((sum, el) => {
      return sum += el.coverage
    }, 0)
})

const packageUuid = computed(() => (name) => props.packages?.find(el => el.packageName == name)?.packageUuid)

function formatForSubmition() {
  const formated = quotations.value.quoatedServices.reduce((total, pack) => {
    pack.services.forEach(el => {
      total.push(el)
    })
    return total;
  }, [])
  return {
    quotationUuid: props.quotation?.quotationUuid,
    institutionUuid: props.institution.institutionUuid,
    description: quotations.value?.description,
    quoatedServices: formated
  }
}

function submitQuotation() {
  console.log(formatForSubmition())
  props.onSubmitQuotation(formatForSubmition())
}

function submitIssueQuotation() {
  console.log(formatForSubmition())
  props.onSubmitQuotationIssue(formatForSubmition())
}

function submitAcceptQuotation() {
  props.onSubmitAcceptQuotation(formatForSubmition())
}

function submitProceedToMembers() {
  const data = formatForSubmition()
  props.onSubmitProceedToMembers({
    institutionUuid: quotation.value?.institutionUuid,
    contractName: quotation.value?.description,
    contractCode: props.quotation?.quotationCode,
    benefit: grandBenefitTotal.value,
    premium: grandTotal.value,
    beginDate: addDate(30).toISOString(),
    endDate: addDate(365 + 30).toISOString(),
    multiGroupUuid: "",
    status: Status.PENDING
  })
}

watch([selectedPackage, selectedPlan], () => {
  if (selectedPackage.value && selectedPlan.value) {
    showSelectedPackageAddBtn.value = true
  } else {
    showSelectedPackageAddBtn.value = false
  }
})

watch(quotations, () => {
  console.log(quotations.value)
}, { deep: true })

const femaleOnly = computed(() => {
  return quotations.value.quoatedServices.filter(el => {
    return femaleOnlyPlan.includes(el.name)
  })
})

const bothPackages = computed(() => {
  return quotations.value.quoatedServices.filter(el => {
    return !femaleOnlyPlan.includes(el.name)
  })
})

console.log(femaleOnlyPlan)
</script>
<template>
  {{ console.log('female', femaleOnly, 'both', bothPackages) }}
  <FormParent :title="institutionTitle" class="bg-white rounded-md w-full">
    <InstitutionForm :showBtn="showInstitutionBtn && actions" :institution="institution" :pending="pending"
      :on-submit="submitInstitution" :btn-text="btnTextInstitution"></InstitutionForm>
  </FormParent>
  <FormParent v-if="institution" :title="`${quotationTitle} for ${institution?.institutionName || ''}`"
    class="relative mt-3 bg-white rounded-md w-full">
    <FormLayout v-slot="{ submit }" class="px-2">
      <div v-if="actions" class="col-span-2 border-b-4 pb-3 items-center gap-3 grid grid-cols-3">
        <div class="flex items-start flex-col z-10 gap-1">
          <p class="text-sm font-medium">Select Coverage Type</p>
          <select :value="packageNames?.[0]" v-model="selectedPackage"
            class="w-full min-w-[10rem] h-[2.07rem] p-0 px-2 text-sm rounded-md border border-gray-300"
            value="Standard Cover">
            <option v-for="p in packageNames || []">{{ p }}</option>
          </Select>
        </div>
        <div class="flex flex-col gap-1">
          <p class="text-sm font-medium">Plan Type</p>
          <select v-model="selectedPlan"
            class="min-w-[10rem] h-[2.07rem] p-0 px-2 text-sm rounded-md border border-gray-300">
            <option v-for="p in filteredplan" :key="p.value" :value="p.value">{{ p.label }}</option>
          </select>
        </div>
        <button v-if="showSelectedPackageAddBtn" @click.stop.prevent="addQuotation(selectedPackage, selectedPlan)"
          class="w-24 mr-auto justify-center  border self-end  items-center gap-1 flex border-gray-300 px-2 py-1 rounded-md bg-white text-primary">
          <BaseIcon :path="mdiPlus" />
          Add
        </button>
      </div>
      <Input :class="!actions ? 'pointer-events-none' : ''" v-model="quotations.description" name="description"
        label="Description" placeholder="Description" validation="required|max-150" />

      <Quote :class="!actions ? 'pointer-events-none' : ''" :key="quote.name" v-for="quote in bothPackages"
        v-model="quote.services" :pack="quote" :minmax="minmax" :package-name="quote.name" :remove="remove"
        :remove-package="removePackage" :plan-type="quote?.planType || selectedPlan" :rate="rate" :actions="actions"
        :packageUuid="packageUuid" :addQuotation="addQuotation" />

      <MaternityQuote :class="!actions ? 'pointer-events-none' : ''" :key="quote.name" v-for="quote in femaleOnly"
        v-model="quote.services" :pack="quote" :minmax="minmax" :package-name="quote.name" :remove="remove"
        :remove-package="removePackage" :plan-type="quote?.planType || selectedPlan" :rate="rate" :actions="actions"
        :packageUuid="packageUuid" :addQuotation="addQuotation" />

      <div class="justify-end flex items-center gap-12 col-span-2">
        <div class="flex gap-2 items-center">
          <p class="font-semibold text-lg">Grand Total (Sum Insured)</p>
          <p>
            {{
              formatCurrency(grandBenefitTotal)
            }}
          </p>
        </div>
        <div class="flex gap-2 items-center">
          <p class="font-semibold text-lg">Grand Total(Premium)</p>
          <p>
            {{
              formatCurrency(grandTotal)
            }}
          </p>
        </div>
      </div>
      <slot name="submitBtn" :submit="submit">
        <FormSubmitButton :class="[showQuotationBtn && showIssueBtn ? '' : 'col-span-2']"
          v-if="actions && showQuotationBtn" :pending="pending" @click.prevent="submit(() => submitQuotation(packages))"
          :btn-text="btnTextQuotation" />
        <FormSubmitButton :class="[showQuotationBtn && showIssueBtn ? '' : 'col-span-2']" v-if="actions && showIssueBtn"
          class="bg-yellow-600" :pending="pending" @click.prevent="submit(() => submitIssueQuotation(packages))"
          btn-text="Issue Quotation" />
        <FormSubmitButton class="col-span-2" v-if="showAcceptBtn" :pending="pending"
          @click.prevent="submit(() => submitAcceptQuotation(packages))" btn-text="Accept" />
        <FormSubmitButton class="col-span-2 bg-yellow-600" v-if="showProceedBtn" :pending="pending"
          @click.prevent="submit(() => submitProceedToMembers(packages))" btn-text="Proceed To Policy Setup" />
      </slot>
    </FormLayout>
  </FormParent>
</template>

<style>
.grid-7-cols {
  grid-template-columns: repeat(6, 1fr) 3rem;
}
</style>
