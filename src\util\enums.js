export const Status = {
  ACTIVE: "ACTIVE",
  PENDING: "PENDING",
  SUSPENDED: "SUSPENDED",
  HISTORY: "HISTORY",
};

export const QuotationStatus = {
  PENDING: "PENDING",
  ACCEPTED: "ACCEPTED",
  ISSUED: "ISSUED",
  PAID: "PAID",
  UNPAID: 'UNPAID'
};

export const ClaimStatus = {
  PENDING: "PENDING",
  AUTHORIZED: "Authorized",
  ISSUED: "ISSUED",
  PAID: "PAID",
};

export const Gender = {
  MALE: 'MALE',
  FEMALE: 'FEMALE'
}

export const GenderCap = {
  MALE: 'Male',
  FEMALE: 'Female'
}

export const allMemberTYpes = [
  {
    label: 'Member Only',
    value: 1, 
  },
  {
    label: 'Member + 1',
    value: 2,
  },
  {
    label: 'Member + 2',
    value: 3
  },
  {
    label: 'Member + 3',
    value: 4,
  },
  {
    label: 'Member + 4',
    value: 5
  },
  {
    label: 'Member + 5 or more',
    value: 6
  },
]

export const SharedlMemberTYpes = [
  {
    label: 'Main Member',
    value: 'Member'
  },
  {
    label: 'Spouse',
    value: 'Spouse'
  },
  {
    label: 'Children',
    value: 'Children'
  }
]

export const MaternityMemberTypes = [
  {
    label: 'Female Employee',
    value: 'Female_Member'
  },
  {
    label: 'Female Spouse',
    value: 'Female_Spouse'
  }
]

export const Plan = [
  {
    label: 'Individual Plan',
    value: 'Individual_Plan'
  },
  {
    label: 'Family Plan',
    value: 'Family_Plan'
  },
  {
    label: 'Family Shared Plan',
    value: 'Family_Shared_Plan'
  },
]

export const PlanTypes = {
  Individual: 'Individual_Plan',
  Family: 'Family_Plan',
  Shared: 'Family_Shared_Plan'
}

export const IndividualTypes = {
  Member: "Member",
  Children: "Children",
  Spouse: "Spouse"
}