<script setup>
import Dropdown from '@/components/dropdown/DropDown.vue';
import IconRounded from '@/components/base/IconRounded.vue';
import Modal from "@/components/modal.vue";
import EditPackage from '../forms/EditPackage.vue';
import { ref, onBeforeUnmount, onMounted } from "vue"
import { useRouter } from 'vue-router';
import { usePackageStore } from '@stores/packageStore'
const packageStore = usePackageStore()
const router = useRouter()
const props = defineProps({ packageRanges: { type: Array }, })
const open = ref(false);
const item = ref({})
const openModal = (sitem) => {

  open.value = !open.value;
  item.value = sitem;
  console.log('item', item.value)
};






const deletePackage = async (packageUuid) => {
  await packageStore.deletePackage(packageUuid)

}


</script>
<template>
  <div class="">
    <div>
      <Modal :open="open" @close="open = !open" title="Edit for" :name="item.name">
        <EditPackage :item="item" />
      </Modal>
    </div>
  </div>
  <div class="min-h-[400px]   mt-1 rounded-sm shadow-md mb-6 ml-2">

    <table class="min-w-full table-fixed divide-y divide-gray-300">


      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class=" text-left text-sm font-semibold text-gray-900 px-1 py-2">#</th>

          <th scope="col" class=" text-left text-sm font-semibold text-gray-900 px-1 py-2">
            PRODUCT
          </th>

          <th scope="col" class=" text-left text-sm font-semibold text-gray-900 px-1 py-2">
            DESCRIPTION
          </th>
          <th scope="col" class=" text-left text-sm font-semibold text-gray-900 px-1 py-2">
            MINIMUM LIMIT
          </th>
          <th scope="col" class=" text-left text-sm font-semibold text-gray-900 px-1 py-2">
            MAXIMUM LIMIT
          </th>
          <th scope="col" class=" text-left text-sm font-semibold text-gray-900">

          </th>
          <th scope="col" class=" text-left text-sm font-semibold text-gray-900">

          </th>


        </tr>
      </thead>


      <tbody class="divide-y divide-gray-200 bg-white">

        <tr v-for="(packageRange, index) in packageRanges">
          <td class="text-sm ">{{ index + 1 }}</td>
          <td class="text-sm">{{ packageRange.packageName }}</td>
          <td class="text-sm ">{{ packageRange.packageDescription }}</td>
          <td class="text-sm">{{ packageRange.minLimit.toLocaleString('en-US') }} </td>
          <td class="text-sm">{{ packageRange.maxLimit.toLocaleString('en-US') }}</td>

          <td><a class="bg-indigo-600 text-center"><router-link :to="{
            name: 'addpackage', params: { id: packageRange.packageUuid },
            query: { packageName: packageRange.packageName }
          }">
                <svg class="" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20"
                  viewBox="0 0 48 48">
                  <path fill="#3949AB" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z">
                  </path>
                  <path fill="#fff" d="M21,14h6v20h-6V14z"></path>
                  <path fill="#fff" d="M14,21h20v6H14V21z"></path>
                </svg>
              </router-link></a></td>

          <td>
            &nbsp;&nbsp;&nbsp;
            <Dropdown v-slot="{ open, setRef, toggleDropdown }">
              <div class="rounded-md shadow-xl  py-0.5 gap-1 flex flex-col border min-w-[6rem] bg-white" :ref="setRef">
                <button @click.prvent="openModal(packageRange)"
                  class="flex w-full bg-gray-300 px-2 rounded-md items-center gap-2">
                  <div class="h-6 w-6">
                    <icon :icon="['far', 'pen-to-square']" />
                  </div>
                  <p>Edit</p>
                </button>

                <button class="flex w-full bg-gray-300 px-2 rounded-md items-center gap-2">

                  <router-link :to="{
                    name: 'packagedetail', params: { id: packageRange.packageUuid },
                    query: { packageName: packageRange.packageName }
                  }">
                    <div class="h-6 w-6">
                      <icon icon="eye" />
                    </div>


                  </router-link>
                  <p>Detail</p>

                </button>


                <button @click.prevent="deletePackage(packageRange.packageUuid)"
                  class="w-full bg-gray-300 px-2 rounded-md flex items-center gap-2">
                  <div class="text-red-600 h-6 w-6">
                    <icon icon="trash" />
                  </div>
                  <p>Delete</p>
                </button>
              </div>
              <button @click="toggleDropdown" class="text center  rounded-full text-primary">
                actions
              </button>
            </Dropdown>


          </td>

        </tr>
      </tbody>



    </table>





  </div>
</template>