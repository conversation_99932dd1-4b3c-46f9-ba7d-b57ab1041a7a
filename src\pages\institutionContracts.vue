<script setup>
import * as session from '@/scripts/session';
import { useRouter, useRoute } from 'vue-router';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import BaseButton from '@/components/base/BaseButton.vue';
import { mdiHandshakeOutline, mdiPlus } from '@mdi/js';
import Modal from '@/components/modal.vue';
import api from '@/scripts/api';
import loader from '@/components/loader/loader.vue';
import { Icon } from '@iconify/vue';
import { ref, onMounted } from 'vue';
import { useUuidStore } from '../../stores/variables';
import CreateInstitutionContract from '@/components/contract/Create-institution-contract.vue';

const router = useRouter();
const route = useRoute();
const uuidStore = useUuidStore();

const openAddModal = ref(false);
const contracts = ref([]);
const institutionName = ref('');
const stat = ref('')

const loading = ref(false);

const getInstitution = async () => {
  loading.value = true;
  await api.get(`/institution/${route.params.Uuid}`).then((data) => {
    institutionName.value = data.institutionName;
    loading.value = false;
  });
};

const handleModalState = (modalValue) => {
  if (modalValue == 'add') {
    openAddModal.value = !openAddModal.value;
  } else if ('close') {
    openDetailModal.value = !openDetailModal.value;
  } else {
    console.log(modalValue);
  }
};

const fetchInstitutionContracts = async (status) => {
  loading.value = true;
  await api.get(`/payer-institution-contract/lists/${route.params.Uuid}?status=${status}`).then((data) => {
    contracts.value = data;
    loading.value = false;
  });
};

onMounted(() => {
  getInstitution();
  if (route.params.status === 'ACTIVE') {
    stat.value = 'ACTIVE'
  } else {
    stat.value = 'PENDING'
  }
  if (stat.value != '') {
    fetchInstitutionContracts(stat.value);
  }


});
</script>
<template>
  <div class="">
    <Modal :open="openAddModal" @close="handleModalState('add')" title="Contract Information">
      <CreateInstitutionContract @close="handleModalState('add')" @refetch="fetchInstitutionContracts" />
    </Modal>
    <SectionMain class="">
      <SectionTitleLineWithButton :icon="mdiHandshakeOutline" :title="institutionName + ' ' + 'Membership Category'"
        main>
        <div class="flex space-x-4">
          <form class="w-full mb-5">
            <label for="default-search"
              class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
            <div class="relative">
              <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                <Icon icon="mdi:magnify" class="text-2xl" />
              </div>
              <input type="search" id="default-search"
                class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search . . ." required />
              <button type="submit"
                class="text-white absolute end-2.5 bottom-2.5 bg-primary0 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Search
              </button>
            </div>
          </form>
          <div class="mt-2">
            <BaseButton :icon="mdiPlus" color="whiteDark" @click="handleModalState('add')" label="Add Category" />
          </div>
        </div>
      </SectionTitleLineWithButton>

      <div class="min-h-[70vh]">
        <div class="flow-root">
          <div class="overflow-x-auto">
            <div class="ring-1 ring-black ring-opacity-5 sm:rounded-lg max-h[15rem] xl:max-h-[80vh] overflow-auto">
              <table
                class="min-w-full overflow-hidden ring-1 ring-black ring-opacity-5 sm:rounded-lg  xl:max-h-[20rem] overflow-y-auto">
                <thead class="sticky top-0 rounded-t-lg border-t-2 border-solid border-black">
                  <tr class="px-3 divide-gray-200 bg-gray-50">
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">#</th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Membership Category Code
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      <span>Membership Description</span>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Effective Date
                    </th>
                    <th scope="col" class="px-2 py-2 text-center font-semibold text-gray-900 whitespace-nowrap">
                      Status
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white" v-if="!loading">
                  <tr v-for="(service, index) in contracts" :key="service.item"
                    class="divide-gray-200 px-3 hover:bg-white duration-200 items-center even:bg-gray-100">
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-center font-medium text-gray-900">
                      <span class="ml-3">
                        {{ index + 1 }}
                      </span>
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 font-medium text-gray-900">
                      {{ service?.contractCode }}
                    </td>

                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.contractName }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ service?.beginDate }} to {{ service?.endDate }}
                    </td>

                    <td class="whitespace-nowrap text-sm py-2 px-2 font-medium">
                      <div class="flex w-full">
                        <span class="px-3 py-0.5 rounded-full w-full text-center" :class="service?.status == 'ACTIVE'
                          ? 'bg-green-100 text-green-600'
                          : 'bg-red-100 text-red-600'
                          ">
                          {{
                            service?.status == 'ACTIVE' ? 'Active' : 'Pending'
                          }}
                        </span>
                      </div>
                    </td>
                    <td class="whitespace-nowrap py-2 px-2 font-semibold underline underline-offset-4">
                      <button @click="
                        router.push({
                          name: 'assign-eligible-service',
                          params: {
                            id: service?.payerInstitutionContractUuid,
                            Uuid: service?.institutionUuid
                          },
                        })
                        " title="Product Coverages"
                        class="bg-gray-400 p-1 mx-2 rounded-lg hover:bg-secondary hover:shadow-xl duration-200">
                        <Icon icon="iconoir:healthcare" class="text-black text-2xl" />
                      </button>
                      <button @click="
                        router.push({
                          name: 'assign-insured-person',
                          params: {
                            id: service?.payerInstitutionContractUuid,
                            Uuid: service?.institutionUuid
                          },
                        })
                        " title="Insured Persons"
                        class="bg-gray-400 p-1 rounded-lg hover:bg-secondary hover:shadow-sm duration-200">
                        <Icon icon="mdi:users-check-outline" class="text-black text-2xl" />
                      </button>
                      <button title="Add Providers"
                        class="bg-gray-400 p-1 mx-2 rounded-lg hover:bg-secondary hover:shadow-xl duration-200" @click="router.push({
                          name: 'ProvidersInContract',
                          params: {
                            Uuid: service?.payerInstitutionContractUuid,
                            id: service?.institutionUuid
                          },
                        })">
                        <div class="flex flex-row">
                          <span></span>
                          <Icon icon="mdi:hospital-building" class="text-black text-2xl" />
                        </div>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <loader />
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>