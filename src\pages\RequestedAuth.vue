<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import { mdiBadgeAccount, mdiChevronDoubleRight, mdiChevronDoubleLeft } from '@mdi/js';
import * as session from '@/scripts/session';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import loader from '@/components/loader/loader.vue';
import { ref, watch, onMounted } from 'vue';
import api from '@/scripts/api';
import NoData from '@/assets/img/no-data-here.png';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';
import { Icon } from '@iconify/vue';
import { formatCurrency } from '@/util/utils';
import BaseButton from '@/components/base/BaseButton.vue';

const toast = useToast();
const router = useRouter();
const emit = defineEmits(['delete']);

const totalPages = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(25);
const pageSizes = ref([25, 50, 75, 100, 125, 150]);
const claims = ref([]);
const loading = ref(false);
const creditClaims = ref(false);
const claimTypes = ref('');
const orderBy = ref('asc');
const selectedClaims = ref([]);
const search = ref(sessionStorage.getItem("searchTerm") || "");

watch(search, (newSearchTerm, oldSearch) => {
    sessionStorage.setItem("searchTerm", newSearchTerm);
    if (newSearchTerm === '') {
        fetchAuthRequest('');
    } else {
        fetchAuthRequest(newSearchTerm);
    }
});

onMounted(async () => {
    creditClaims.value = true
    fetchAuthRequest('');
});

const fetchAuthRequest = async (searchKey) => {
    loading.value = true;
    await api.get(`/claim-authorization/lists?status=Requested&page=1&limit=25`).then((data) => {
        claims.value = data;
        loading.value = false;
    });
};

const ViewPaymentRequestSlip = (service) => {
    router.push({
        name: 'paymentSlip',
        params: {
            bCode: service?.batchCode,
            Uuid: service?.providerUuid
        }
    })
}

const handleSort = (column) => {
    if (orderBy.value === 'asc') {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return a[column].localeCompare(b[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'desc';
    } else {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return b[column].localeCompare(a[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'asc';
    }
};
</script>
<template>
    <div class="w-full h-max">
        <SectionMain>
            <SectionTitleLineWithButton :icon="mdiBadgeAccount" title="Requested Authorizations" main>
                <form id="searchEliService" class="w-full md:w-[30%] lg:w-[20%] mt-2">
                    <label for="default-search"
                        class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                    <div class="relative">
                        <input type="text" v-model="search" id="default-search"
                            class="block w-full px-4 py-2 ps-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="Search . . ." required />
                    </div>
                </form>
            </SectionTitleLineWithButton>

            <div class="min-h-[70vh]">
                <div v-if="loading" class="flex items-center justify-center h-[40vh]">
                    <loader />
                </div>

                <div class="h-full bg-gray-50" v-else>
                    <div class="min-h-[70vh] flex items-center justify-center flex-col gap-5"
                        v-if="claims && claims.length === 0 && !loading">
                        <img :src="NoData" alt="" />
                        <div class="text-sm text-primary font-semibold">
                            No {{ claimTypes }} claims found
                        </div>
                    </div>
                    <div class="px-4 sm:px-6 lg:px-8" v-if="claims && claims.length">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="border-t-2 border-solid border-black">
                                        <tr>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                #
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Full Name
                                            </th>

                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Policy Holder Name
                                            </th>

                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Provider Name
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Total Amount
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900 cursor-pointer"
                                                @click="handleSort('claimDate')">
                                                Requested Date
                                                <span v-if="orderBy === 'asc'">▲</span>
                                                <span v-if="orderBy === 'desc'">▼</span>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Status
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900 cursor-pointer">
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                        <tr v-for="(claim, index) in claims" :key="claim.claimUuid" :class="[
                                            ' cursor-pointer hover:bg-gray-100 even:bg-gray-50',
                                            selectedClaims.includes(claim.claimUuid)
                                                ? ' bg-primary0 hover:bg-primary0'
                                                : 'text-gray-900 ',
                                        ]">
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ index + 1 }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.insuredFullName }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.institutionName }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.providerName }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ formatCurrency(claim?.totalAmount) }}
                                            </td>
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.requestPaymentDate }}
                                            </td>
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.authorizationStatus }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                <div class="flex">
                                                    <div class="bg-primary h-8 p-1 m-1 text-white text-center cursor-pointer rounded-md"
                                                        @click="router.push({
                                                            name: 'claim-detail-page',
                                                            params: {
                                                                id: claim?.claimUuid,
                                                                Uuid: claim?.providerCounter,
                                                                caId: claim?.claimAuthorizationUuid ? claim?.claimAuthorizationUuid : '10',
                                                                status: 'Authorization'
                                                            },
                                                        })">
                                                        <div class="text-base capitalize">Details</div>
                                                    </div>
                                                    <button @click="ViewPaymentRequestSlip(claim)"
                                                        title="Payment Request Slip"
                                                        class="bg-primary p-1 m-1 rounded-lg hover:bg-primary hover:shadow-xl duration-200">
                                                        <Icon icon="mdi:file-pdf-box" class="text-white text-2xl" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="w-full flex justify-end mt-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="bg-white">
                                            <BaseButton :icon="mdiChevronDoubleLeft" label="Previous"
                                                title="previous page" color="whiteDark" @click="previousPage"
                                                :disabled="currentPage === 1" />
                                        </div>
                                        <div>
                                            <div>
                                                Items per page:
                                                <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                                                    <option v-for="size in pageSizes" :key="size" :value="size">
                                                        {{ size }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div>
                                            <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page"
                                                color="whiteDark" @click="nextPage"
                                                :disabled="currentPage === totalPages" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SectionMain>
    </div>
</template>
