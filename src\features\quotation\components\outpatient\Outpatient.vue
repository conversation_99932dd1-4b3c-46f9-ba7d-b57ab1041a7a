<script setup>
  import { allMemberTYpes, SharedlMemberTYpes, Plan } from '@/util/enums'
  import { computed, onMounted, ref, watch } from 'vue';
  import { formatCurrency, formatNumber, genId } from '@/util/utils'
  import { Input } from '@/components/form_elements';
  import BaseIcon from '@/components/base/BaseIcon.vue';
  import { mdiClose, mdiMinusThick, mdiPlus } from '@mdi/js';


  const props = defineProps({
    modelValue: {
      required: true
    },
    actions: {
      type: Boolean,
      default: true
    },
    pack: {
      type: Object, 
    },
    planType: {
      type: String,
      required: true
    },
    removePackage: {
      type: Function,
      required: true
    },
    rate: {
      type: Function,
      required: true
    },
    minmax: {
      type: Function,
      required: true
    },
    addQuotation: {
      type: Function,
      required: true
    },
    packageUuid: {
      type: Function,
      required: true
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const packageName = 'Outpatient'

  const planType = ref(props.planType || '')

  console.log(props.pack.services)
  const outpatientPackage = ref({
    name: packageName,
    planType: planType.value,
    services: props.pack ? props.pack.services?.map(el => {
      return {
        ...el,
        id: genId.next().value,
        packageUuid: props.packageUuid(packageName),
        descriptio: planType.value == 'Family_Plan' ? el.description || 1 : el.individualType || 'Member',
        coverage: el.coverage || '',
        individualType: props.planType == 'Family_Plan' ? 'NA' : el.individualType || 'Member',
        numberOfInsured: el.numberOfInsured || 0,
        premium: el.premium || '',
        rate: el.rate || '',
        package: packageName,
      }
    }) : [{
      id: genId.next().value,
      packageUuid: props.packageUuid(packageName),
      descriptio: planType.value == 'Family_Plan' ? 1 : 'Member',
      coverage: '',
      individualType: props.planType == 'Family_Plan' ? 'NA' : 'Member',
      numberOfInsured: 0,
      premium: '',
      rate: '',
      package: packageName,
    }]
  })

  const familyTypes = computed(() => {
    return (name, id) => { 
      if(planType.value == 'Family_Plan') {
        const selecteDescriptions = outpatientPackage.value.services.map(el => el.descriptio) || []
        return allMemberTYpes.filter(el => !selecteDescriptions.includes(el.value) || el.value == id)
      } else {
        const selecteDescriptions = outpatientPackage.value.services.map(el => el.descriptio) || []
        return SharedlMemberTYpes.filter(el => !selecteDescriptions.includes(el.value) || el.value == id)
      }
    }
  })

  const totalSumInsured = computed(() => {
    return (name, id) => {
      const service = outpatientPackage.value.services.find(el => el.id == id)
      const sum = service.coverage * service.numberOfInsured
      // if(planType.value == 'Family_Plan') return sum * service.descriptio
      return sum
    }
  })

  const benefitTotal = computed(() => {
    return (name) => {
      return outpatientPackage.value.services.reduce((sum, el) => {
        return sum += totalSumInsured.value(name, el.id)
      }, 0)
    }
  })

  function remove(id) {
    console.log(id)
    if(!props.actions) return
    
    const idx = outpatientPackage.value.services.findIndex(el => el.id == id)

    if(idx > -1 && outpatientPackage.value.services.length == 1) {
      props.removePackage(packageName)
    } else {
      outpatientPackage.value.services = outpatientPackage.value.services.filter(el => el.id != id)
    }
  }

  const subTotal = computed(() => {
    return (name) => {
      return outpatientPackage.value.services
      .reduce((sum, el) => {
        return sum += premium.value(name, el.id)
      }, 0)
    }
  })

  const premium = computed(() => {
    return (name, id) => {
      const service = outpatientPackage.value.services.find(el => el.id == id)
      if(!service) return 0
      const sum = service.numberOfInsured * service.coverage * props.rate(packageName, service.coverage, planType.value == 'Family_Plan' ? service.descriptio : 1) / 100

      return sum
    } 
  })

  function add(name) {
    if(planType.value == 'Family_Plan' && outpatientPackage.value.services.length == allMemberTYpes.length) return
    if(planType.value != 'Family_Plan' && outpatientPackage.value.services.length == SharedlMemberTYpes.length) return

    const defaultData = {
      id: genId.next().value,
      packageUuid: props.packageUuid(name),
      descriptio: familyTypes.value(name)?.[0]?.value,
      coverage: '',
      individualType: 'NA',
      numberOfInsured: 0,
      premium: '',
      rate: '',
      package: name,
    }

    if(planType.value != 'Family_Plan') {
      outpatientPackage.value.services.push({
        id: genId.next().value,
        packageUuid: props.packageUuid(name),
        descriptio: familyTypes.value(name, '')?.[0]?.value,
        coverage: '',
        individualType: familyTypes.value(name, '')?.[0]?.label,
        numberOfInsured: 0,
        premium: '',
        rate: '',
        package: name,
      })
    } else {
      outpatientPackage.value.services.push(defaultData)    
    }
  }

  watch(outpatientPackage, () => {
    console.log(planType.value)
    outpatientPackage.value.services.forEach(el => {
      el.rate = props.rate(packageName, el.coverage, planType == 'Family_Plan' ? el.descriptio : 1)
      el.premium = premium.value(packageName, el.id)
      el.planType = planType.value
      el.individualType = planType.value == 'Family_Plan' ? 'NA' : el.descriptio
      el.description = planType.value == 'Family_Plan' ? el.descriptio : 1
    })
    emit('update:modelValue', outpatientPackage.value.services)
  }, {
    immediate: true,
    deep: true
  })
</script>

<template>
  <div class="col-span-2">
    <div class="flex items-center border-b h-12 gap-4 justify-between">
      <div class="flex flex-1 items-center gap-4">
        <p class="text-lg py-1 font-medium">{{ outpatientPackage.name }}</p>
        <!-- <p class="ml-auto border border-gray-300 px-4 py-1 rounded-md">{{ outpatientPackage.planType }}</p> -->
        <p class="ml-auto ">Plan Type</p>
        <select class="min-w-[10rem] h-[2.07rem] p-0 px-2 text-sm rounded-md border border-gray-300" v-model="planType">
          <option :value="p.value" v-for="p in Plan" :key="p.value">{{ p.label }}</option>
        </select>
      </div>
      <button v-if="actions" @click.prevent="removePackage(outpatientPackage.name)"
        class="w-8 h-8 flex justify-center items-center rounded-full bg-gray-300">
        <BaseIcon :size="22" :path="mdiClose" />
      </button>
    </div>
    <div class="grid grid-7-cols">
      <div class="col-span-4 items-end grid grid-cols-4">
        <p># Insured</p>
        <p>Description</p>
        <p>Sum Insured</p>
        <p class="px-2">Total Sum Insured</p>
      </div>
      <div class="col-span-3 items-end grid grid-cols-3">
        <p class="px-2">Rate</p>
        <p class="px-2">Premium</p>
      </div>
    </div>
    <div :key="quotation.id" v-for="(quotation, idx) in outpatientPackage.services"
      class="grid grid-7-cols justify-center flex-col gap-2 col-span-2">
      <!-- <div class="gap-4 grid grid-7-cols"> -->
      <div class="flex-1 gap-2 col-span-4 grid grid-cols-4">
        <Input v-model="quotation.numberOfInsured" validation="required|num"
          placeholder="insured persons" />
        <select class="h-8 border-gray-300 px-2 p-0 text-sm text-black rounded-md" v-model="quotation.descriptio">
          <option :value="option.value" :key="option.value" v-for="option in familyTypes(outpatientPackage.name, quotation.descriptio)">
            {{ option.label }}
          </option>
        </select>
        <Input v-model="quotation.coverage" :validation="{
          required: true,
          num_minmax: [minmax(outpatientPackage.name)?.min, minmax(outpatientPackage.name)?.max]
        }"
          placeholder="Sum Insured" />
        <p class="p-2">
          {{ formatCurrency(totalSumInsured(outpatientPackage.name, quotation.id)) }}
        </p>
      </div>
      <div class="col-span-2 grid grid-cols-3">
        <p class="p-2">
          {{ rate(outpatientPackage.name, quotation.coverage, planType == 'Family_Plan' ? quotation.descriptio : 1) }}
        </p>
        <p class="p-2">
          {{ formatCurrency(premium(outpatientPackage.name, quotation.id)) }}
        </p>
      </div>
      <button v-if="actions" @click.prevent="remove(quotation.id)"
        class="w-6 h-6 flex justify-center items-center rounded-full bg-gray-300">
        <BaseIcon class="text-red-500" :size="18" :path="mdiMinusThick" />
      </button>
    </div>
    <div class="flex border-t py-1 gap-12 justify-between">
      <p class="">
        Total Number of Insured
        {{ 
          formatNumber(outpatientPackage.services.reduce((sum, el) => sum += parseInt(el.numberOfInsured), 0))  
        }}
      </p>
      <div class="flex gap-3" >
        <p class="font-semibold">SubTotal Sum Insured</p> <p>
          {{
            formatCurrency(benefitTotal(outpatientPackage.name))
          }}
        </p>
      </div>
      <div class="flex gap-3">
        <p class="font-semibold">Premium Subtotal</p> <p>
          {{
            formatCurrency(subTotal(outpatientPackage.name))
          }}
        </p>
      </div>
    </div>
    <div class="col-span-2 flex pt-2 border-t justify-end">
      <button v-if="actions" @click.prevent="add(outpatientPackage.name, packageUuid(outpatientPackage.name))"
        class="border items-center gap-1 flex border-gray-300 px-2 py-1 rounded-md bg-white text-primary">
        <BaseIcon :size="20" :path="mdiPlus" />
        Add
      </button>
    </div>
  </div>
</template>
<style>
.grid-7-cols {
  grid-template-columns: repeat(6, 1fr) 3rem;
}
</style>