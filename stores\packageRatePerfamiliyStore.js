import packageRatePerFamiliyService from "@/services/packageRatePerFamiliyService";
import { defineStore } from "pinia";
export const useRatePerFamiliyStore=defineStore('rateperperfamilystore',
  {
    state: () => ({
        packageRatePerFamily: [],
        id: 0,
        loading:false
      }),

      // Action functions 

      actions: {
        async  getAllPackagesRatePerFamily() {
         
          this.packageRatePerFamily=await packageRatePerFamiliyService.getAllPackageRatePerFamily()
          this.loading=true
          
           },


        async addPackageRatePerFamiliy(data) {
        
          this.packageRatePerFamily=await packageRatePerFamiliyService.addPackageRatePerFamiliy(data)
          
           },

        async deletePackagePerFamily(id)
          {
               try {
                await packageRatePerFamiliyService.deletePackageId(id)
                return true
                
               } catch (error) {
                  return false
               }
          },

          // status update 
          async updatePackagePerfamiliyStatus(data,id){

            try {
                 
              this.pending=true  
              const d=await packageRatePerFamiliyService.updatePackageStatus(data,id)  
              this.pending=false
             return d;
             
           } catch (error) {
             this.pending=false
            
           }
    
               },


          async updatePackagePerfamiliy(data,id){

            try {
                 
              this.pending=true  
              const d=await packageRatePerFamiliyService.updatePackage(data,id)  
              this.pending=false
             return d;
             
           } catch (error) {
             this.pending=false
            
           }
    
               },
          async getPackageById(id) {

           this.loading=true
            let data=await packageRatePerFamiliyService.getPackageById(id)
            const formattedData = data.map(item => {
              return {
                  ...item,
                  maxLimit: item.maxLimit.toLocaleString('en-US'),
                  minLimit: item.minLimit.toLocaleString('en-US')
              };
          });
              return formattedData 
           
          },
          async addPackageRange(data) {
            this.PackageRange.push(data)
          },

          async deletePackagePerFamily(id) {
            try {
            let response= await packageRatePerFamiliyService.deletePackagePerfamilyById(id)
             if(response)
                {
                  return true
                }
                else {
                  return false
                }
             
            } catch (error) {
               return false
            }
            
          },


      }


  })