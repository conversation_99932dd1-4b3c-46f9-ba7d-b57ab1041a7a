<script setup>
import * as session from "@/scripts/session";
import api from "@/scripts/api";
import { ref } from "vue";
import { useToast } from "vue-toastification";
import { useRouter } from "vue-router";
import Logo from "@/assets/img/new_logo.jpg";
import Frame from "@/assets/img/Frame.jpg";
import { Icon } from "@iconify/vue";
import { mdiEyeOutline } from "@mdi/js";
import SvgIcon from "@jamescoyle/vue-icon";
import { email } from "@vee-validate/rules";

const toast = useToast();

const router = useRouter();
const username = ref("");
const password = ref("");
const isLoading = ref(false);

session.setUserIsLoggedIn({
  accessToken:
    "eyJhbGciOiJIUzI1NiJ9.*******************************************************************************.8FOx04uWVM5EtJsi8JsHDv_1mScs4YMhQzTbGyxnEtQ",
  privileges: ["All Privilegs"],
  personalInfos: {
    firstName: " Abera",
    lastName: "Abebe",
    role: "Admin",
  },
});

const handleLogin = async () => {
  isLoading.value = true;
  try {
    const response = await api.authPost("/users/signIn", {
      userName: username.value,
      password: password.value,
    });
    
    const { token, privileges, firstName, fatherName, roleName } = response;

    const personalInfos = {};
    personalInfos.firstName = firstName;
    personalInfos.lastName = fatherName;
    personalInfos.role = roleName;

    const user = {
      accessToken: token,
      privileges,
      personalInfos,
    };
    user.accessToken
      ? session.setUserIsLoggedIn(user)
      : session.setUserIsLoggedIn(false);
    toast.success("you have successfully logged in");
    router.push("/");
  } catch (error) {
    toast.error(error.message);
    isLoading.value = false;
  }
};

let passwordFieldType = "password";
const togglePasswordVisibility = () => {
  passwordFieldType = passwordFieldType === "password" ? "text" : "password";
  document.getElementById("Password").type = passwordFieldType;
};
</script>
<template>
  <div class="relative h-screen w-screen bg-white">
    <div class="bg-primary w-[50%] h-screen">
      <div class="inset-0 absolute flex justify-center items-center">
        <div class="bg-white rounded-2xl shadow-xl lg:w-[28%]">
          <form class="w-full h-full" @submit.prevent="onSubmit" novalidate>
            <div class="p-8 flex flex-col h-full w-full gap-[15px]">
              <div class="flex justify-center">
                <img class="w-[200px]" :src="Logo" />
              </div>
              <div class="w-full h-[20px] flex justify-center">
                <img class="w-[150px] h[92px]" :src="Frame" />
              </div>
              <div class="flex justify-center">
                <h1 class="text-lg font-semibold" style="color: #050b1d">
                  Family Insurance S.C
                </h1>
              </div>
              <div class="flex gap-2 flex-col">
                <Label
                  class="opacity-[80%] font-normal text-[16px]"
                  style="color: #050b1d"
                  >Username</Label
                >
                <input
                  id="username"
                  v-model="username"
                  name="username"
                  autocomplete="username"
                  required=""
                  placeholder="Username"
                  class="block w-full h-[40px] text-TextPrimary rounded-[5px] border-1 py-1.5 shadow-sm placeholder:text-[14px] placeholder:opacity-30 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:leading-6"
                />

                <Label
                  class="opacity-[80%] font-normal text-[16px]"
                  style="color: #050b1d"
                  >Password</Label
                >
                <div class="flex relative items-center">
                  <input
                    id="Password"
                    v-model="password"
                    name="password"
                    type="password"
                    autocomplete="password"
                    required=""
                    placeholder="Password"
                    class="block w-full h-[40px] text-TextPrimary rounded-[5px] border-1 py-1.5 shadow-sm placeholder:text-[14px] placeholder:opacity-30 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:leading-6"
                  />
                  <div class="absolute right-4">
                    <svg-icon
                      @click="togglePasswordVisibility"
                      class="text-TextPrimary items center cursor-pointer"
                      type="mdi"
                      :path="mdiEyeOutline"
                    ></svg-icon>
                  </div>
                </div>

                <div class="w-full flex justify-end">
                  <Label class="text-[14px] opacity-80 text-TextPrimary"
                    >Forget password?</Label
                  >
                </div>
                <div class="mt-2 w-full h-[40px] flex justify-center">
                  <button
                    @click="handleLogin"
                    class="w-full font-semibold cursor-pointer bg-primary h-[60px] items-center justify-center flex text-white rounded-md"
                  >
                    <Icon
                      icon="svg-spinners:3-dots-scale"
                      class="text-2xl"
                      v-if="isLoading"
                    />
                    <span class="font-normal text-[20px]" v-if="!isLoading"
                      >Login</span
                    >
                  </button>
                </div>
                <div
                  class="flex justify-center text-TextPrimary opacity-70 mt-10 text-xs px-4"
                >
                  <h1>Powered by Medco Technology Solutions PLC.</h1>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.loginbackground {
  background-color: "LoginBackground";
  height: 100%;
  width: 50%;
}
</style>
