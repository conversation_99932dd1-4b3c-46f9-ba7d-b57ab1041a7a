<script setup>
  import { useApiRequest } from '@/composables/useApiRequest'
  import { getClaims } from '@/features/claim/api/claimApi'

  const props = defineProps({
    status: {
      type: String,
      requried: true
    }
  })
  const claimReq = useApiRequest()

  claimReq.send(
    () => getClaims({status: props.status})
  )
</script>
<template>
  <slot
    :claims="claimReq.response.value || []"
    :pending="claimReq.pending.value"
  />
</template>