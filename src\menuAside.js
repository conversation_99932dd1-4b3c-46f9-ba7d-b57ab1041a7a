import {
  mdiMonitor,
  mdiAccountGroupOutline,
  mdiOfficeBuilding,
  mdiAccountGroup,
  mdiFile,
  mdiHospitalBuilding,
  mdiDrawPen,
  mdiToolbox,
  mdiPackage,
  mdiBadgeAccount,
  mdiAccountWrench,
  mdiChartBox,
  mdiFileChart,
  mdiBallot,
  mdiFileCheck,
  mdiFileDocumentPlus,
  mdiFileDocumentEdit,
  mdiFileDocumentRefresh,
  mdiFileEye,
  mdiCreditCardOutline,
  mdiOfficeBuildingPlus,
  mdiOfficeBuildingRemove,
  mdiFileSign,
  mdiFileCancelOutline,
  mdiCog,
  mdiFileDocumentCheck,
  mdiPlus,
  mdiContentSaveAll,
  mdiBullhorn,
  mdiCheck,
  mdiSendClock,
  mdiCashCheck,
  mdiFinance,
  mdiFileDownload,
  mdiBrightnessAuto,
  mdiFrequentlyAskedQuestions,
  mdiCheckDecagram,
  mdiBookRemove,
  mdiFileDocumentEditOutline,
  mdiStar,
  mdiCheckboxMultipleMarked,
  mdiCurrencyEth,
  mdiCurrencyUsdOff,
  mdiCurrencyUsd,
  mdiCash,
} from "@mdi/js";
import * as session from "@/scripts/session";
import roles from "@/helpers/roles";

export default function () {
  const user = session.getUser();
  // const values = Object.values(user.privileges);
  // let role;
  // values.forEach((value) => {
  //   role = value;
  //   console.log(value);
  // });

  return [
    {
      to: "/",
      icon: mdiMonitor,
      label: "Dashboard",
      visible: true,
      isOpen: false,
    },
    {
      icon: mdiBullhorn,
      label: "Quotation",
      visible: true,
      menu: [
        {
          to: "/Quotation/New-Quotation",
          icon: mdiFileDocumentEditOutline,
          label: "New Quotation",
          visible: true,
        },
        {
          to: "/Quotation/Saved-Quotations",
          icon: mdiStar,
          label: "Saved Quotations",
          visible: true,
        },
        {
          to: "/Quotation/Issued-Quotations",
          icon: mdiSendClock,
          label: "Issued Quotations",
          visible: true,
        },
        {
          to: "/Quotation/Accepted-Quotations",
          icon: mdiCheckboxMultipleMarked,
          label: "Accepted Quotations",
          visible: true,
        },
      ],
    },
    {
      // to: "/Policy Administration",
      icon: mdiAccountGroup,
      label: "Underwriting",
      visible: true,
      isOpen: true,
      menu: [
        {
          icon: mdiFileDocumentPlus,
          to: "/Underwriting/New-Policy",
          label: "Add New Policy",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileDownload,
          to: "/Underwriting/Import-Policy",
          label: "Import Policy",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileDocumentCheck,
          to: "/Underwriting/Issued-Policies",
          label: "Issued Policies",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileDocumentEdit,
          to: "/Underwriting/Amend-Policy",
          label: "Alteration",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiCog,
          to: "/Underwriting/Amend-Policy",
          label: "Renewals",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
    {
      icon: mdiBrightnessAuto,
      label: "Authorization",
      visible: true,
      isOpen: true,
      menu: [
        {
          icon: mdiFrequentlyAskedQuestions,
          to: "/Authorization/Requested",
          label: "Requested Authorizations",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiCheckDecagram,
          to: "/Authorization/Approved",
          label: "Approved Authorizations",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiBookRemove,
          to: "/Authorization/Rejected",
          label: "Rejected Authorizations",
          isMenu: true,
          isOpen: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
    {
      label: "Claim Management",
      icon: mdiBallot,
      isMenu: false,
      isOpen: false,
      // visible:
      //   user?.privileges?.some((e) =>
      //     roles.read_insititution_contract.includes(e)
      //   ) ||
      //   user?.privileges?.some((e) =>
      //     roles.read_provider_contract.includes(e)
      //   ) ||
      //   false,
      visible: true,
      menu: [
        {
          icon: mdiFileDocumentPlus,
          to: "/ClaimManagement/Cash-Claims",
          label: "Cash Claims",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFile,
          to: "/ClaimManagement/Credit-Services",
          label: "Credit Services",
          isMenu: true,
          visible: true,
        },
        {
          icon: mdiFileEye,
          to: "/ClaimManagement/Process-Claims",
          label: "Process Claims",
          isMenu: true,
          visible:
            user?.personalInfos.role === "PayerClaimReviewer" ||
            user?.personalInfos.role === "Superman",
        },
        {
          icon: mdiFileEye,
          to: "/ClaimManagement/Processed-Claims",
          label: "Verify Claims",
          isMenu: true,
          visible:
            user?.personalInfos.role === "PayerClaimReviewer" ||
            user?.personalInfos.role === "Superman",
        },
        {
          icon: mdiCreditCardOutline,
          to: "/ClaimManagement/Checked-Claims",
          label: "Approve Claims",
          isMenu: true,
          visible:
            user?.personalInfos.role === "PayerClaimChecker" ||
            user?.personalInfos.role === "Superman",
        },
        {
          icon: mdiFileCheck,
          to: "/ClaimManagement/Approved-Claims",
          label: "Authorize Claims",
          isMenu: true,
          visible:
            user?.personalInfos.role === "PayerClaimApprover" ||
            user?.personalInfos.role === "Superman",
        },
        {
          icon: mdiFileDocumentRefresh,
          to: "/ClaimManagement/Authorized-Claims",
          label: "Completed Claims",
          isMenu: true,
          visible:
            user?.personalInfos.role === "PayerClaimAuthorizer" ||
            user?.personalInfos.role === "Superman",
        },
      ],
    },
    {
      icon: mdiFinance,
      label: "Finance",
      visible: true,
      isOpen: false,
      menu: [
        {
          icon: mdiCreditCardOutline,
          to: "/Finance/Premium-Receipt",
          label: "Premium Receipt",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiCurrencyUsd,
          to: "/Finance/Claim-Payment",
          label: "Claim Payment",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiCashCheck,
          to: "/Finance/Paid-Claims",
          label: "Paid Claims",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiCash,
          to: "/report/provider",
          label: "Other Payment",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
    {
      // to: "/Group-Service",
      icon: mdiFileSign,
      label: "Provider Contracts",
      isOpen: false,
      visible:
        user?.privileges?.some((e) => roles.read_institution.includes(e)) ||
        false,
      menu: [
        {
          icon: mdiFileSign,
          to: "/ProviderContracts/Create-Contract",
          label: "Create Contracts",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileCheck,
          to: "/ProviderContracts/Active-Contracts",
          label: "Active Contracts",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileCancelOutline,
          to: "/ProviderContracts/Suspended-Contracts",
          label: "Suspended Contracts",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
    {
      icon: mdiOfficeBuilding,
      label: "Provider Setting",
      isMenu: false,
      isOpen: false,
      visible:
        user?.privileges?.some((e) => roles.read_institution.includes(e)) ||
        false,
      menu: [
        {
          icon: mdiOfficeBuildingPlus,
          to: "/ProviderSetting/Add-Provider",
          label: "Add Provider",
          isMenu: false,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiHospitalBuilding,
          to: "/ProviderSetting/Active-Providers",
          label: "Active Providers",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiOfficeBuildingRemove,
          to: "/ProviderSetting/Inactive-Providers",
          label: "Inactive Providers",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
    {
      label: "Product Setting",
      icon: mdiToolbox,
      isMenu: false,
      visible:
        user?.privileges?.some((e) => roles.read_claim.includes(e)) || false,

      menu: [
        {
          to: "/ProductSetting/Coverages",
          icon: mdiPackage,
          label: "Coverages",
          isMenu: true,
          visible:
            user?.privileges?.some((e) => roles.read_claim.includes(e)) ||
            false,
        },
      ],
    },
    {
      icon: mdiChartBox,
      label: "Reports",
      isOpen: false,
      visible:
        user?.privileges?.some((e) => roles.read_claim.includes(e)) || false,
      menu: [
        {
          icon: mdiFileChart,
          to: "/report/client",
          label: "Report by Clients",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_insititution_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileChart,
          to: "/report/provider",
          label: "Report by Provider",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
        {
          icon: mdiFileChart,
          to: "/report/branch",
          label: "Report by Branch",
          isMenu: true,
          visible:
            user?.privileges?.some((e) =>
              roles.read_provider_contract.includes(e)
            ) || false,
        },
      ],
    },
  ];
}
