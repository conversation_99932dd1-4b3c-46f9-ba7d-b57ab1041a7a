<script setup>
import { useRouter, useRoute } from "vue-router";
import SectionMain from "@/components/section/SectionMain.vue";
import api from "@/scripts/api";
import {
  mdiPlus,
  mdiSecurity,
} from "@mdi/js";
import { onMounted, ref, computed, watch } from "vue";
import SectionTitleLineWithButton from "@/components/section/SectionTitleLineWithButton.vue";
import { useToast } from "vue-toastification";
import BaseButton from "@/components/base/BaseButton.vue";

const toast = useToast()
const router = useRouter()
const route = useRoute()

const isLoading = ref(false)
const institutions = ref([])
const selectAll = ref(false)
const selected = ref([])
const institutionName = ref('')
const institutionPhone = ref('')
const insuranceNumber = ref('')
const showInput = ref(false)
const showInput1 = ref(false)
const showInput2 = ref(false)
const payerProviderContractUuid = ref('')
const contractCode = ref('')
const search = ref(sessionStorage.getItem("searchTerm") || "")

watch(search, (newSearchTerm) => {
  sessionStorage.setItem("searchTerm", newSearchTerm);
  if (newSearchTerm === "") {
    fetchInstitutionsForMapping('');
  } else {
    fetchInstitutionsForMapping(newSearchTerm);
  }
});

payerProviderContractUuid.value = route.params.payerProviderContractUuid;
contractCode.value = route.params.payerProviderContractCode;

const toggleButton = () => {
  showInput.value = true;
};
const toggleButton1 = () => {
  showInput1.value = true;
};
const toggleButton2 = () => {
  showInput2.value = true;
};

const addInstitutions = async (institution) => {
  isLoading.value = true;
  try {
    await api.post("/map_contract", {
      payerProviderContractUuid: payerProviderContractUuid.value,
      payerInstitutionContractUuid: institution.payerInstitutionContractUuid,
    }).then((data) => {
      isLoading.value = false;
      toast.success(data.message);
    });
  } catch (error) {
    isLoading.value = false;
    toast.error(error.message);
  }
};

const postSelectedInstitutions = async () => {
  selected.value.forEach((institution) => {
    addInstitutions(institution);
  })
  router.back();
};

const fetchInstitutionsForMapping = async (searchKey) => {
  isLoading.value = true;
  try {
    await api.get(`/map_contract/institutions-for-mapping-contracts?search=${searchKey}&page=1&limit=25`,).then((data) => {
      isLoading.value = false;
      institutions.value = data;
    });
  } catch (error) {
    isLoading.value = false;
    toast.error(error);
  }
};

const select = () => {
  selected.value = [];
  if (!selectAll.value) {
    institutions.value.forEach((institution) => {
      selected.value.push(institution);
    })
  }
};

const filteredItems = computed(() => {
  if (
    !institutionName.value &&
    !institutionPhone.value &&
    !insuranceNumber.value
  ) {
    return institutions.value;
  } else {
    return institutions.value.filter(
      (item) =>
        item.institutionName
          .toLowerCase()
          .includes(institutionName.value.toLowerCase()) &&
        item.institutionPhone
          .toLowerCase()
          .includes(institutionPhone.value.toLowerCase()) &&
        item.insuranceNumber
          .toLowerCase()
          .includes(insuranceNumber.value.toLowerCase())
    );
  }
});

onMounted(() => {
  fetchInstitutionsForMapping('')
})
</script>

<template>
  <div class="w-full h-max">
    <SectionMain>
      <SectionTitleLineWithButton :icon="mdiSecurity" :title="'Select and Add Institutions to Contract'" main>
        <div class="flex space-x-3">
          <form id="searchInst" class="w-full md:w-[30%] lg:w-[40%] mt-1">
            <label for="default-search"
              class="mb-1 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
            <div class="relative">
              <input type="text" v-model="search" id="default-search"
                class="block w-full px-4 py-2 ps-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search . . ." required />
            </div>
          </form>
          <div>
            <BaseButton :icon="mdiPlus" color="whiteDark" @click="postSelectedInstitutions"
              label="Add Selected Institutions" />
          </div>
        </div>
      </SectionTitleLineWithButton>
      <div class="flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50 border-t-2 border-solid border-black ">
                  <tr>
                    <th scope="col" class="px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
                      S.N
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="toggleButton">
                      <div>
                        <input v-if="showInput" type="text" v-model="institutionName" placeholder="Institution Name"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>Institution Name</p>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('payerPhone')">
                      <div @click="toggleButton1">
                        <input v-if="showInput1" type="text" v-model="institutionPhone" placeholder="Institution Phone"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>Policy Description</p>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('payerName')">
                      <div @click="toggleButton2">
                        <input v-if="showInput2" type="text" v-model="insuranceNumber" placeholder="Insurance Number"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>Policy Number</p>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      @click="sort('endDate')">
                      Premium
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      <input type="checkbox" v-model="selectAll" @click="select" />
                      <i class="form-icon"></i>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200" v-if="filteredItems != []">
                  <tr v-for="(institution, index) in filteredItems" :key="institution.payerInstitutionContractUuid"
                    class="bg-white even:bg-gray-100">
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ index + 1 }}
                    </td>
                    <td class="whitespace-wrap px-3 py-2 text-sm text-gray-500">
                      {{ institution.institutionName }}
                    </td>
                    <td class="whitespace-wrap px-3 py-2 text-sm text-gray-500">
                      {{ institution.payerInstitutionContractName }}
                    </td>
                    <td class="whitespace-wrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                      {{ institution.payerInstitutionContractCode }}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {{ institution.premium }}
                    </td>
                    <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                      <input type="checkbox" :value="institution" v-model="selected" />
                      <i class="form-icon"></i>
                    </td>
                  </tr>
                </tbody>
              </table>
              <p v-if="filteredItems && filteredItems.length == 0"
                class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">
                No Items Were Found
              </p>
            </div>
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>
