import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useInstitutions = defineStore('initutionsStore', () => {
  const institutions = ref([])
  const done = ref(false)
  
  function add(institution) {
    institutions.value.push(institution)
  }

  function setDone(val) {
    done.value = val
  }

  function reset() {
    institutions.value = []
    done.value = false
  }

  function add(institution) {
    if(institution instanceof Array) {
      institutions.value.push(...institution)
    } else {
      institutions.value.unshift(institution)
    }
  }
  
  function get(id) {
    return institutions.value.find(el => el.institutionUuid == id)
  }

  function set(institution) {
    institutions.value = institution
  }

  function update(id, institution) {
    const idx = institutions.value.findIndex(el => el.institutionUuid == id)

    if(idx == -1) return 

    institutions.value.splice(idx, 1, institution)
  }

  function remove(id) {
    institutions.value = institutions.value.filter(el => el.institutionUuid != id)
  }

  return {
    institutions,
    add,
    reset,
    update,
    setDone,
    get,
    set,
    remove,
    done
  }
})