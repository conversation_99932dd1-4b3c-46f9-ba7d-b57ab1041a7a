import ApiService from '@/service/ApiService'
import { getQueryFormObject } from '@/util/utils'

const path = '/claim'

export async function getPaymentDetail(batchCode, config = {}) {
}

export async function payClaim(data, config = {}) {
  const api = new ApiService()
  return api.addAuthenticationHeader().put(`${path}/settle/payment`, data, {
    ...config,
    // headers: {
    //   'Content-Type': 'multiplart/form-data'
    // }
  })
}

export async function getClaims(query = {}, config = {}) {
  const api = new ApiService()
  const qs = getQueryFormObject(query)
  return api.addAuthenticationHeader().get(`${path}/payment/requested/list${qs}`, config)
}