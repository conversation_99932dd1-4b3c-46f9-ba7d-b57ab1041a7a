<script setup>
import { mdiPlus } from '@mdi/js';
import QuotationPage from './QuotationPage.vue';
import SavedQuotationsDataProvider from '../components/SavedQuotationsDataProvider.vue';
import TableWrapper from '@/components/TableWrapper.vue';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { QuotationStatus } from '@/util/enums';

</script>
<template>
  <QuotationPage :headers="{
    head: ['created date'],
    row: ['createdDate']
  }" :status="QuotationStatus.PENDING" :DataProvider="SavedQuotationsDataProvider" class="flex flex-col" :icon="mdiPlus">
    <template #actions="{row}">
      <DropdownBtn @click="$router.push('/view_quotation/' + row?.institutionUuid + '/' + row?.quotationUuid)">
        View Quotation
      </DropdownBtn>
    </template>
  </QuotationPage>
</template>