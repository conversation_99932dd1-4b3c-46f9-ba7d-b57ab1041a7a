{"name": "tailwind-tuto", "description": "A Vue 3 Starter Boilerplate with TailwindCSS 3, Vue Router 4, Pinia 2, <PERSON><PERSON> 4, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 29 and <PERSON>.", "private": false, "version": "1.0.9", "license": "MIT", "keywords": ["boilerplate", "vue", "pinia", "axios", "vite", "jest", "tailwind"], "repository": {"type": "git", "url": "https://github.com/morellexf13/tailwind-tuto.git"}, "scripts": {"dev": "vite --host", "test": "jest --verbose", "test:coverage": "jest --coverage", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@customizer/modal-x": "^0.0.41", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-regular-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/vue-fontawesome": "^3.0.5", "@headlessui/vue": "^1.7.16", "@iconify/vue": "^4.1.1", "@jamescoyle/vue-icon": "^0.1.2", "@mdi/js": "^7.3.67", "@tailwindcss/forms": "^0.5.6", "@vee-validate/rules": "^4.11.8", "@vuepic/vue-datepicker": "^7.2.2", "@vueuse/components": "^10.7.2", "@vueuse/core": "^10.7.2", "apexcharts": "^3.44.0", "date-fns": "^2.30.0", "install": "^0.13.0", "npm": "^10.4.0", "number-to-words": "^1.2.4", "numeral": "^2.0.6", "pdfmake": "^0.2.10", "pinia-plugin-persistedstate": "^3.2.0", "tailwind-tuto": "file:", "to-words": "^4.0.0", "v-viewer": "^3.0.11", "v3-img-preview": "^1.1.16", "vee-validate": "^4.11.8", "viewerjs": "^1.11.6", "vue": "^3.3.4", "vue-fontawesome": "^0.0.2", "vue-jwt-decode": "^0.1.0", "vue-toastification": "^2.0.0-rc.5", "vue3-apexcharts": "^1.4.4", "yup": "^1.3.2"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.22.20", "@pinia/testing": "^0.1.3", "@sucrase/jest-plugin": "^3.0.0", "@types/jest": "^29.5.5", "@types/node": "^20.8.3", "@vitejs/plugin-vue": "^4.4.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/test-utils": "^2.4.1", "@vue/vue3-jest": "^29.2.6", "autoprefixer": "^10.4.16", "axios": "^1.5.1", "babel-jest": "^29.7.0", "babel-preset-vite": "^1.1.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "pinia": "^2.1.6", "pinia-plugin-persist": "^1.0.0", "postcss": "^8.4.31", "sass": "^1.69.0", "tailwindcss": "^3.3.3", "ts-jest": "^29.1.1", "vite": "^4.4.11", "vue-router": "^4.2.5", "vue-router-mock": "^1.0.0"}}