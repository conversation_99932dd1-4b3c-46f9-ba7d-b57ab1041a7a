<script setup>
  import { Input, Select, InputFile } from '@/components/form_elements'
  import FormLayout from '@/components/FormLayout.vue'
  import FormSubmitButton from '@/components/FormSubmitButton.vue';
  import { ref } from 'vue';

  const props = defineProps({
    pending: {
      type: Boolean,
      default: false
    },
    payment: Object,
    btnText: {
      type: String,
      required: true
    },
    onSubmit: {
      type: Function,
      required: true
    }
  })

  const paymentType = ref('')

  function formSubmit({ values }) {
    props.onSubmit(values)
  }
</script>
<template>
  <FormLayout v-slot="{ submit }">
    <Input
      name="amount"
      placeholder="Amount"
      label="Amount"
      :validation="{
        required: true,
        num: true,
        exactly: {
          args: payment?.amount
        }
      }"
    />
    <Select
      v-model="paymentType"
      name="paymentType"
      validation="required"
      placeholder="Payment Type"
      label="Payment Type"
      :options="['Cash', 'Check', 'Bank Transfer']"
    />
    <Input
      v-if="paymentType == 'Check'"
      name="checkNumber"
      validation="required"
      placeholder="Check Number"
      label="Check Number"
    />
    <InputFile
      name="file"
      placeholder="attachemtn"
      label="Attachement"
    />
    <FormSubmitButton
      :pending="pending"
      @click.prevent="submit(formSubmit)"
      :btn-text="btnText"
      class="col-span-2"
    />
  </FormLayout>
</template>