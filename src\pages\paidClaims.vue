<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import { mdiBadgeAccount, mdiChevronDoubleLeft, mdiChevronDoubleRight } from '@mdi/js';
import * as session from '@/scripts/session';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import loader from '@/components/loader/loader.vue';
import { ref, watch, onMounted } from 'vue';
import BaseButton from '@/components/base/BaseButton.vue';
import api from '@/scripts/api';
import NoData from '@/assets/img/no-data-here.png';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';
import { Icon } from '@iconify/vue';
import { formatCurrency } from '@/util/utils';
import customSelect from '@/components/forms/custom/select.vue';

const user = session.getUser();
const toast = useToast();
const router = useRouter();
const emit = defineEmits(['delete']);

const totalPages = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(25);
const pageSizes = ref([25, 50, 75, 100, 125, 150]);
const claims = ref([]);
const providers = ref([]);
const creditClaims = ref(false);
const cashClaims = ref(false);
const loading = ref(false);
const claimTypes = ref('');
const orderBy = ref('asc');
const distinctProviders = ref([]);
const selectedClaims = ref([]);
const institutions = ref([]);
const institutionUuid = ref('');
const providerUuid = ref(null);

onMounted(async () => {
    creditClaims.value = true;
    await fetchClaims(providerUuid.value, '');
    fetchProviders();
});

watch(providerUuid, (newUuid, oldUuid) => {
    if (newUuid != oldUuid) (
        fetchClaims(newUuid, '')
    )
})

watch(institutionUuid, (newUuid, oldUuid) => {
    if (newUuid != oldUuid) (
        fetchCashClaims(newUuid, '')
    )
})

const fetchClaims = async (id, searchKey) => {
    try {
        loading.value = true;
        await api.get(`/claim/payment/settled/list?search=${searchKey}&providerUuid=${id}&page=1&limit=25`).then((data) => {
            claims.value = data;
            claimTypes.value = 'Paid';
            loading.value = false;
        });
    } catch (error) {
        loading.value = false;
        toast.error(error.message);
    }
};
const fetchCashClaims = async (id, searchKey) => {
    try {
        loading.value = true;
        await api.get(`/cash-credit/paid/lists?institutionUuid=${id}&searchKey=${searchKey}&page=1&limit=25`).then((data) => {
            claims.value = data;
            claimTypes.value = 'Paid';
            loading.value = false;
        });
    } catch (error) {
        loading.value = false;
        toast.error(error.message);
    }
};

const fetchInstitutions = async () => {
    try {
        loading.value = true;
        await api.get(`/institution/policy-holders/list?page=1&limit=25&status=ACTIVE`).then((data) => {
            institutions.value = data;
            loading.value = false;
        });
    } catch (error) {
        loading.value = false;
        toast.error(error.message);
    }
}

const handleSort = (column) => {
    if (orderBy.value === 'asc') {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return a[column].localeCompare(b[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'desc';
    } else {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return b[column].localeCompare(a[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'asc';
    }
};

watch(claims, () => {
    distinctProviders.value = [
        ...new Set(claims.value.map((claim) => claim.providerName)),
    ].map((providerName, index) => {
        const providerUuid = claims.value.find(
            (claim) => claim.providerName === providerName
        ).providerUuid;
        return {
            id: index + 1,
            name: providerName,
            uuid: providerUuid,
        };
    });
});

const filteredClaims = ref(claims);

const fetchProviders = async () => {
    loading.value = true;
    await api.get(`/provider/list/name?page=1&limit=25`).then((data) => {
        providers.value = data;
        loading.value = false;
    });
}

const ViewCPV = (id) => {
    router.push({
        name: 'paymentVoucherSlip',
        params: {
            bCode: id
        }
    })
}

const creditTab = () => {
    creditClaims.value = true;
    cashClaims.value = false;
    fetchClaims(providerUuid.value, '');
}

const cashTab = () => {
    creditClaims.value = false;
    cashClaims.value = true;
    fetchCashClaims(institutionUuid.value, '');
    fetchInstitutions();
}
</script>
<template>
    <div class="w-full h-max">
        <SectionMain>
            <SectionTitleLineWithButton :icon="mdiBadgeAccount" :title="claimTypes + ' ' + 'Claims'" main>
                <div class="sm:col-span-1 m-1" v-if="creditClaims">
                    <customSelect label="Provider Name" placeholder="All" :items="providers" v-model="providerUuid"
                        valueKey="providerUuid" displayKey="providerName" />
                    <!-- <label for="insurance" class="text-sm font-normal">Provider Name</label>
                    <div
                        class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[200px]">
                        <select name="providerUuid" id="providerUuid" v-model="providerUuid"
                            class="truncate block w-full px-4 py-2 rounded-md bg-gray-100 border-1 focus:border-gray-500 focus:bg-white focus:ring-0">
                            <option :value=null class="py-1 hover:bg-red-500 hover:text-white">
                                All
                            </option>
                            <option v-for="provider in providers" :key="provider.providerUuid"
                                :value="provider.providerUuid" class="py-1 hover:bg-red-500 hover:text-white">
                                {{ provider.providerName }}
                            </option>
                        </select>
                    </div> -->
                </div>

                <div class="sm:col-span-1 m-1" v-else>
                    <customSelect label="Institution Name" placeholder="All" v-model="institutionUuid"
                        valueKey="institutionUuid" displayKey="institutionName" :items="institutions" />
                    <!-- <label for="insurance" class="text-sm font-normal">Institution Name</label>
                    <div
                        class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[200px]">
                        <select name="institutionUuid" id="institutionUuid" v-model="institutionUuid"
                            class="truncate block w-full px-4 py-2 rounded-md bg-gray-100 border-1 focus:border-gray-500 focus:bg-white focus:ring-0">
                            <option value='' class="py-1 hover:bg-red-500 hover:text-white">
                                All
                            </option>
                            <option v-for="provider in institutions" :key="provider.institutionUuid"
                                :value="provider.institutionUuid" class="py-1 hover:bg-red-500 hover:text-white">
                                {{ provider.institutionName }}
                            </option>
                        </select>
                    </div> -->
                </div>
            </SectionTitleLineWithButton>
            <div class="flex p-2 space-x-1">
                <div class="w-max px-3 py-2 border-2 border-solid border-gray-500 bg-gray-50 sm:ml-90 rounded-lg cursor-pointer hover:text-black hover:text-medium"
                    :class="creditClaims
                        ? 'border-b-2 border-gray-900 px-2 py-1 bg-primary text-white'
                        : 'text-black'
                        " @click="creditTab()">
                    <p class="">Credit Services</p>
                </div>
                <div class="w-max px-3 py-2 border-2 border-solid border-gray-500 bg-gray-50 rounded-lg cursor-pointer hover:text-gray-900 hover:text-medium"
                    :class="cashClaims
                        ? 'border-b-2 border-gray-900 px-2 py-1 bg-primary text-white'
                        : 'text-black'
                        " @click="cashTab()">
                    <p class="">Cash Services</p>
                </div>
            </div>
            <div class="min-h-[70vh] bg-gray-50">
                <div v-if="loading" class="flex items-center justify-center h-[40vh]">
                    <loader />
                </div>

                <div class="h-full" v-else>
                    <div class="min-h-[70vh] flex items-center justify-center flex-col gap-5"
                        v-if="claims && claims.length === 0 && !loading">
                        <img :src="NoData" alt="" />
                        <div class="text-sm text-primary font-semibold">
                            No {{ claimTypes }} claims found
                        </div>
                    </div>
                    <div class="px-4 sm:px-6 lg:px-8" v-if="claims && claims.length">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="border-t-2 border-solid border-black">
                                        <tr>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                No.
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                                                v-if="creditClaims">
                                                Provider Name
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                                                v-else>
                                                Institution Name
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Payment Number
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Paid Amount
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Paid Date
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900">
                                                Status
                                            </th>

                                            <th scope="col"
                                                class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900 cursor-pointer">
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                        <tr v-for="(claim, index) in filteredClaims" :key="claim.claimUuid" :class="[
                                            ' cursor-pointer hover:bg-gray-100 even:bg-gray-100',
                                            selectedClaims.includes(claim.claimUuid)
                                                ? ' bg-primary0 hover:bg-primary0'
                                                : 'text-gray-900 ',
                                        ]">
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500">
                                                {{ index + 1 }}
                                            </td>
                                            <td class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ claim?.providerName }}({{ claim?.providerPhone }})
                                            </td>
                                            <td class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-else>
                                                {{ claim?.institutionName }}({{ claim?.institutionPhone }})
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500">
                                                {{ claim?.paymentCode }}
                                            </td>

                                            <td class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ formatCurrency(claim?.amount) }}
                                            </td>
                                            <td class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-else>
                                                {{ formatCurrency(claim?.totalAmount) }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ claim?.paidDate }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-else>
                                                {{ claim?.actionDate }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ claim?.claimStatus }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500"
                                                v-else>
                                                {{ claim?.status }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-b border-gray-600 text-base text-gray-500">
                                                <div class="flex">
                                                    <div class="bg-primary h-8 p-1 m-1 text-white text-center cursor-pointer rounded-md"
                                                        @click="router.push({
                                                            name: 'paidClaimView',
                                                            params: {
                                                                id: claim?.paymentCode
                                                            },
                                                        })">
                                                        <div class="text-base capitalize">Details</div>
                                                    </div>
                                                    <button @click="ViewCPV(claim?.paymentCode)"
                                                        title="Payment Request Slip"
                                                        class="bg-primary p-1 m-1 rounded-lg hover:bg-primary hover:shadow-xl duration-200">
                                                        <Icon icon="mdi:file-pdf-box" class="text-white text-2xl" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="w-full flex justify-end mt-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="bg-white">
                                            <BaseButton :icon="mdiChevronDoubleLeft" label="Previous"
                                                title="previous page" color="whiteDark" @click="previousPage"
                                                :disabled="currentPage === 1" />
                                        </div>
                                        <div>
                                            <div>
                                                Items per page:
                                                <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                                                    <option v-for="size in pageSizes" :key="size" :value="size">
                                                        {{ size }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div>
                                            <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page"
                                                color="whiteDark" @click="nextPage"
                                                :disabled="currentPage === totalPages" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SectionMain>
    </div>
</template>
