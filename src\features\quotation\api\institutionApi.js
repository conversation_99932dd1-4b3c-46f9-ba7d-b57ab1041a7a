import ApiService from '@/service/ApiService.js'
import { getQueryFormObject } from '@/util/utils'

const path = '/institution'

export async function createInstitution(data, config = {}) {
  const api = new ApiService()
  return await api.addAuthenticationHeader().post(path, data, config)
}

export async function updateInstitution(id, data, config = {}) {
  const api = new ApiService()
  return await api.addAuthenticationHeader().put(`${path}/${id}`, data, config)
}

export async function getAllInstitutions(query = {}, config = {}) {
  console.log(config)
  const api = new ApiService()
  const qs = getQueryFormObject(query)
  return await api.addAuthenticationHeader().get(`${path}/list${qs}`, config)
}

export async function getInstitutionsId(id, config = {}) {
  const api = new ApiService()
  return await api.addAuthenticationHeader().get(`${path}/${id}`, config)
}