<script setup>
import { useRouter, useRoute, onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import { makeAuthenticatedRequest } from '@/scripts/api'
import { mdiMenu, mdiTrayArrowUp, mdiSecurity, mdiAccountMultipleCheckOutline, mdiNumeric9PlusCircleOutline, mdiDownload, mdiChevronDoubleRight, mdiChevronDoubleLeft } from '@mdi/js';
import { onMounted, ref, computed, toRefs, watch } from 'vue';
import Modal from '@/components/modal.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { useToast } from 'vue-toastification';
import Loader from '@/components/loader/loader.vue';
import BaseButton from '@/components/base/BaseButton.vue';
import { useRowStore } from '../../stores/threePageValue';
import EditPrice from '../components/forms/editPrice.vue';
import SvgIcon from '@jamescoyle/vue-icon';
import axios from 'axios';
import ConfirmModal from '@/components/ConfirmModal.vue';

const downloadUrl = import.meta.env.VITE_DOWNLOAD_FILE_URL;
const router = useRouter()
const route = useRoute()
const toast = useToast()
const rowStore = useRowStore()

const serviceName = ref('')
const totalPages = ref(null)
const currentPage = ref(1)
const itemsPerPage = ref(25)
const pageSizes = ref([25, 50, 75, 100, 125, 150])
const openForm = ref(false)
const isLoading = ref(false)
const services = ref([])
const serviceUuid = ref('')
const Category = ref('')
const subCategory = ref('')
const nameService = ref('')
const showInput = ref(false)
const showInput1 = ref(false)
const showInput2 = ref(false)
const openImportModal = ref(false)
const approveModal = ref(false)
const selectAll = ref(false)
const selected = ref([])
const importedFile = ref(null)
const dropdownIndex = ref(null)
const providerName = ref('')
const providerId = ref('')
const selectedServicePrice = ref(null);
const search = ref(localStorage.getItem('searchTerm') || '');

watch(search, (newSearchTerm) => {
    localStorage.setItem('searchTerm', newSearchTerm);
    if (newSearchTerm === '') {
        fetchEligibleServices(currentPage.value, itemsPerPage.value);
    }
});

// beforeRouteLeave = (to, from, next) => {
//     localStorage.removeItem('searchTerm');
//     next();
// }

const props = defineProps({
    payerProviderContractUuid: {
        type: String,
        required: true,
    }
});

providerId.value = route.params.providerId
const { payerProviderContractUuid } = toRefs(props)
// onBeforeRouteUpdate((to, from, next) => {
//     console.log(to.params.providerName)
//     console.log(stateProviderName)
//     if (to.params.providerName !== stateProviderName) {
//         next({ name: 'NotFound' });
//     } else {
//         next();
//     }
// });

const fetchProviderName = async () => {
    try {
        await makeAuthenticatedRequest({
            method: 'GET',
            url: `/api/payer/claimconnect/provider/${providerId.value}`
        }).then((data) => {
            isLoading.value = false
            providerName.value = data.providerName;
        })

    } catch (error) {
        isLoading.value = false;
        toast.error(error.message)
    }
}

const downloadServices = async () => {
    try {
        const response = await axios.get(`${downloadUrl}/api/provider/claimconnect/service/export/${providerId.value}`, { responseType: 'blob' });
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'Services.xls');
        document.body.appendChild(link);
        link.click();
    } catch (error) {
        isLoading.value = false;
        toast.error(error.message)
    }
}

const toggleCategory = () => {
    showInput.value = true
}
const toggleSubCategory = () => {
    showInput1.value = true
}
const toggleName = () => {
    showInput2.value = true
}

const closeModal = async () => {
    openForm.value = !openForm.value
    await fetchEligibleServices(currentPage.value, itemsPerPage.value)
}

const openModal = (service) => {
    selectedServicePrice.value = service.servicePriceUuid;
    serviceName.value = service.item;
    openForm.value = !openForm.value
    dropdownIndex.value = null
}

const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++;
    }

    fetchEligibleServices(currentPage.value, itemsPerPage.value)
}

const previousPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--;
    }
    fetchEligibleServices(currentPage.value, itemsPerPage.value)
}

const fetchEligibleServices = async (currentPage, myItemsPerPage) => {
    isLoading.value = true
    try {
        await makeAuthenticatedRequest({
            method: 'GET',
            url: `/api/payer/claimconnect/eligible-service/contract/${payerProviderContractUuid.value}?search=${search.value}&page=${currentPage}&limit=${myItemsPerPage}`
        }).then((data) => {
            isLoading.value = false
            services.value = data
            if (data.length != 0) {
                totalPages.value = data[0].totalPages
            }
        })

    } catch (error) {
        isLoading.value = false;
        toast.error(error.message)
    }
}

const deleteService = (eligibleServiceUuid) => {
    serviceUuid.value = eligibleServiceUuid
    approveModal.value = !approveModal.value;
}

const deleteConfirmedService = async (serviceUuid) => {
    isLoading.value = true;
    dropdownIndex.value = null;
    approveModal.value = !approveModal.value
    try {
        await makeAuthenticatedRequest({
            method: 'DELETE',
            url: `/api/payer/claimconnect/eligible-service/${serviceUuid}`,
        }).then((data) => {
            isLoading.value = false;
            toast.success(data.message);
            if (data) {
                fetchEligibleServices(currentPage.value, itemsPerPage.value)
            }
        });

    } catch (error) {
        isLoading.value = false;
        toast.error(error);
    }
};

const handleItemsPerPageChange = () => {
    const itemsPage = itemsPerPage.value
    fetchEligibleServices(currentPage.value, itemsPage)
}

const handleFileChange = async (event) => {
    importedFile.value = event.target.files[0];
}

const submitServiceAttachment = async (event) => {
    event.preventDefault();
    isLoading.value = true;
    let formData = new FormData();
    formData.append('file', importedFile.value);
    console.log(importedFile.value)
    try {
        await makeAuthenticatedRequest({
            method: 'POST',
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            url: `/api/payer/claimconnect/eligible-service/import-eligible-services?payerProviderContractUuid=${payerProviderContractUuid.value}`,
            data: formData
        }).then((data) => {
            isLoading.value = false;
            toast.success(data.message);
        });
    } catch (error) {
        isLoading.value = false;
        toast.error(error.message);
    }
}

const filteredItems = computed(() => {
    if (!Category.value && !subCategory.value && !nameService.value) {
        return services.value;
    } else {
        return services.value.filter(item =>
            item.category.toLowerCase().includes(Category.value.toLowerCase()) &&
            item.subCategory.toLowerCase().includes(subCategory.value.toLowerCase()) &&
            item.item.toLowerCase().includes(nameService.value.toLowerCase())
        )
    }
})

const displayMenu = (index) => {
    dropdownIndex.value = dropdownIndex.value === index ? null : index;
}

onMounted(() => {
    handleItemsPerPageChange()
    fetchProviderName()
})
</script>

<template>
    <div class="w-full h-max">
        <ConfirmModal v-model="approveModal" @confirm="deleteConfirmedService(serviceUuid)" icon="simple-line-icons:check"
            title="Delete Service" description="Are you sure you need to Delete this Service?" confirmButton="Delete"
            iconClass="text-red p-1 text-3xl" iconWrapperClass="bg-red rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-red-800 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-900 sm:ml-3 sm:w-auto duration-300" />

        <Modal :open="openForm" @close="closeModal" :icon="mdiNumeric9PlusCircleOutline" className="h-9 w-9"
            :title="'update price of ' + serviceName" class="pt-10">
            <EditPrice @save="closeModal" :priceItems="selectedServicePrice" />
        </Modal>
        <Modal :open="openImportModal" @close="openImportModal = false" title="Import Services to contract">
            <form @submit="submitServiceAttachment" enctype="multipart/form-data" class="mt-10 flex flex-col gap-y-10">
                <div
                    class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[383px]">
                    <input type="file" name="attachment" id="attachment"
                        class="px-4 block flex-1 border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                        placeholder="attachment" @change="handleFileChange($event)" />
                </div>
                <div class="flex w-full justify-center">
                    <button
                        class="bg-primary/80 hover:bg-primary hover:shadow-lg duration-200 px-10 py-1 text-lg text-white font-semibold rounded-lg">
                        Import
                    </button>
                </div>
            </form>
        </Modal>
            <SectionMain>
                <SectionTitleLineWithButton :icon="mdiSecurity" :title="providerName + ' ' + 'Contracted Services'" main>
                    <div class="flex gap-x-5">
                        <BaseButton :icon="mdiDownload" color="whiteDark"
                            :title="'Download ' + providerName + ' ' + 'Services and update price in Excel and import them'"
                            @click="downloadServices" :label="'Download ' + providerName + ' ' + 'Services'" />
                        <BaseButton :icon="mdiTrayArrowUp" title="Import Contracted Service and Price" color="whiteDark"
                            @click="openImportModal = true" label="Import Services" />
                    </div>
                </SectionTitleLineWithButton>
                <div class="w-full flex justify-start">
                    <form class="w-[30%] mb-2">
                        <label for="default-search"
                            class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                        <div class="relative">
                            <input type="text" v-model="search" id="default-search"
                                class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Search for Services here . . ." required />
                            <button type="submit" @click="fetchEligibleServices"
                                class="text-white absolute end-2.5 bottom-2.5 bg-primary/70 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
                <div class="mt-4 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('contractCode')">S.N</th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="toggleCategory">
                                                <div>
                                                    <input v-if="showInput" type="text" v-model="Category"
                                                        placeholder="Service Category"
                                                        class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Category
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('payerPhone')">
                                                <div @click="toggleSubCategory">
                                                    <input v-if="showInput1" type="text" v-model="subCategory"
                                                        placeholder="Service Sub Category"
                                                        class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Sub Category
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                <div @click="toggleName">
                                                    <input v-if="showInput2" type="text" v-model="nameService"
                                                        placeholder="Service Name" class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Name
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('endDate')">Unit Price</th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                <input type="checkbox" v-model="selectAll" @click="select">
                                                <i class="form-icon"></i>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white" v-if="!isLoading">
                                        <tr v-for="(service, index) in filteredItems"
                                            :key="service.payerProviderContractUuid"
                                            class="h-2 box-content divide-gray-200 px-3 hover:bg-primary/5  duration-200 items-center even:bg-gray-50">
                                            <td class="whitespace-nowrap px-3 py-1.5 text-sm text-gray-500">{{ index + 1
                                            }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-1.5 text-sm text-gray-500">{{
                                                service.category }}</td>
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">{{
                                                service.subCategory }}</td>
                                            <td
                                                class="whitespace-nowrap py-1 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                {{ service.item }}</td>
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">{{
                                                service.unitPrice
                                            }}</td>
                                            <td
                                                class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                <input type="checkbox" :value="service" v-model="selected">
                                                <i class="form-icon"></i>
                                            </td>
                                        </tr>

                                    </tbody>
                                    <tbody v-else>
                                        <div class="relative justify-center">
                                            <Loader />
                                        </div>
                                    </tbody>
                                </table>
                                <p v-if="filteredItems && filteredItems.length === 0"
                                    class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">No Items
                                    Were Found</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full flex justify-end mt-2">
                    <div class="flex items-center space-x-2">
                        <div class="bg-white">
                            <BaseButton :icon="mdiChevronDoubleLeft" label="Previous" title="previous page"
                                color="whiteDark" @click="previousPage" :disabled="currentPage === 1" />
                        </div>
                        <div>
                            <div>
                                Items per page:
                                <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                                    <option v-for="size in pageSizes" :key="size" :value="size">
                                        {{ size }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page" color="whiteDark"
                                @click="nextPage" :disabled="currentPage === totalPages" />
                        </div>
                    </div>
                </div>
            </SectionMain>
    </div>
</template>../stores/threePageValue