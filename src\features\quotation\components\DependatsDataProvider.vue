<script setup>
  import { useApiRequest } from '@/composables/useApiRequest';
  import { getDependants } from '@/features/quotation/api/dependantApi'
import { Status } from '@/util/enums';

  const props = defineProps({
    insuredUuid: {
      type: String,
      required: true
    }
  })
  
  const dependantReq = useApiRequest()
  
  dependantReq.send(
    () => getDependants(props.insuredUuid, Status.ACTIVE),
    console.log
  )
</script>
<template>
  <slot
    :dependants="dependantReq.response.value"  
    :pending="dependantReq.pending.value"  
  />
</template>