<script setup>
import pdfMake from 'pdfmake/build/pdfmake'
import pdfFonts from 'pdfmake/build/vfs_fonts'
import { getBgbase64 } from '@/util/utils'
import { computed, ref } from 'vue';
import { formatCurrency, formatNumber, secondDateFormat } from '@/util/utils'
import { IndividualTypes, PlanTypes } from '@/util/enums';

pdfMake.vfs = pdfFonts.pdfMake.vfs

const props = defineProps({
  packages: {
    type: Array,
    required: true
  },
  institution: {
    type: Object,
    required: true
  },
  quotation: {
    type: Object,
    required: true
  }
})

const femaleGroup = Object.groupBy(props.packages, ({ gender }) => gender == 'FEMALE')
const femaleonlyPackages = femaleGroup.true.map(el => el.packageUuid)

function numberOfInsured(data) {
  const type = data?.[0]?.planType
  const id = data?.[0]?.packageUuid

  console.log(type, id)

  if (femaleonlyPackages.includes(id)) {
    return data.find(el => !el.spouse).numberOfInsured
  } else if (type == PlanTypes.Family) {
    return data.reduce((sum, el) => sum += el.numberOfInsured, 0)
  } else if (type == PlanTypes.Individual) {
    return data.filter(el => el.individualType == IndividualTypes.Member).reduce((sum, el) => sum += el.numberOfInsured, 0)
  }
}

function numberOfDependants(data) {
  const type = data?.[0]?.planType
  const id = data?.[0]?.packageUuid

  if (femaleonlyPackages.includes(id)) {
    return data.find(el => el.spouse)?.numberOfInsured || 0
  } else if (type == PlanTypes.Family) {
    return data.reduce((sum, el) => sum += (el.numberOfInsured * el.description), 0) - numberOfInsured(data)
  } else if (type == PlanTypes.Individual) {
    return data.filter(el => el.individualType != IndividualTypes.Member).reduce((sum, el) => sum += el.numberOfInsured, 0)
  }
}

function sumInsured(data) {
  return data.reduce((sum, el) => sum += (el.numberOfInsured * el.description), 0) - numberOfInsured(data)
}

const file = ref()
async function getPdf() {
  const bg = await getBgbase64('/crtificate_header_anbessa.png')

  const docDefinition = {
    content: [
      {
        style: ['defaultTopMargin', { fontSize: 12 }],
        alignment: 'right',
        stack: [
          {
            style: { bold: true },
            text: `Issued Date: ${secondDateFormat(props.quotation?.issuedDate)}`,
          },
          {
            text: [
              {
                text: `Quotation Number: `,
              },
              {
                style: { bold: true },
                text: props.quotation?.quotationCode
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        style: { bold: true, fontSize: 13 },
        text: `To ${props.institution.institutionName}`
      },
      {
        style: { decoration: 'underline' },
        text: props.institution.state
      },
      {
        marginTop: 8,
        style: { fontSize: 14, bold: true },
        text: 'Dear Sir/Madam'
      },
      {
        alignment: 'center',
        marginTop: 8,
        style: { fontSize: 14 },
        text: [
          {
            text: 'Subject: '
          },
          {
            style: { decoration: 'underline', bold: true },
            text: 'Financial Proposal For Medical Coverage'
          }
        ]
      },
      {
        marginTop: 8,
        stack: [
          {
            text: 'Reference has been made to your procurement invitation by which you have requested us to provide you with our financial proposal in repect of Group Medical Insurance Cover to your Employees.'
          },
          {
            marginTop: 8,
            text: 'Accordingly, as per the bid doucument and employee information you have provided shown hereunder summery our financial proposal:'
          }
        ]
      },
      {
        marginTop: 8,
        table: {
          widths: [20, 'auto', 'auto', '*', 'auto', 'auto'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Coverage', style: 'tableHeader' }, { text: '# Insured', style: 'tableHeader' }, { alignment: 'right', text: 'Sum Insured', style: 'tableHeader' }, { text: 'Plan Type', style: 'tableHeader' }, { alignment: 'right', text: 'Premium (ETB)', style: 'tableHeader' }],
            ...Object.values(
              props.quotation?.quoatedServices?.reduce((state, el, idx) => {
                if (state[el.packageUuid]) {
                  state[el.packageUuid].push(el)
                } else {
                  state[el.packageUuid] = [el]
                }
                return state
              }, {})
            )
              .reduce((state, el, idx) => {
                const row = []
                row.push(idx + 1)
                row.push(props?.packages?.find(pack => pack.packageUuid == el?.[0]?.packageUuid)?.packageName || '')
                row.push(`M = ${numberOfInsured(el)},\nD = ${numberOfDependants(el)}`)
                row.push({ alignment: 'right', text: formatCurrency(el?.[0]?.coverage) })
                row.push(el?.[0]?.planType?.replace(/_/g, ' ') || '')
                row.push({ alignment: 'right', style: { bold: true }, text: formatCurrency(el.reduce((sum, el) => sum += el.premium, 0)) })
                state.push(row)
                return state
              }, []),
            [{ colSpan: 5, alignment: 'center', text: 'Revenue Stamp' }, {}, {}, {}, {}, { style: { bold: true }, alignment: 'right', text: formatCurrency(5) }],
            [{ colSpan: 5, alignment: 'center', text: 'Grand Total' }, {}, {}, {}, {}, { style: { bold: true, fontSize: 14 }, alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0) + 5) }],
          ]
        }
      },
      {
        marginTop: 8,
        style: { bold: true },
        text: 'Please also note that this proposal remains valid for 90 calender days from bid closing date and kindly attached herewith please find details of premium breakdown to each cover type to your easy reference.'
      },
      {
        marginTop: 8,
        text: 'Hope the above quote is presented to the best of your intrest and in the line with TOR, if you have any question or need further clarification, please dont\'t hesitate to contact us through our office line 011 663 90 65 or mobile 0911 20 73 92'
      },
      {
        marginTop: 8,
        text: 'finally we would like to take this opportunity to thank you and express our appreciation for giving us the chance toserve you and assure you of our usual utmost and prompt service.'
      },
      {
        marginTop: 20,
        style: { bold: true },
        text: 'Sincerely Yours,'
      },
      {

      }
    ],
    background: [
      {
        image: bg,
        width: 600
      }
    ],
    styles: {
      defaultTopMargin: {
        margin: [0, 100, 0, 0]
      },
      tableHeader: {
        bold: true
      }
    }
  }

  pdfMake.createPdf(docDefinition).getBlob(blob => {
    file.value = URL.createObjectURL(blob)
  })
}

getPdf()
</script>
<template>
  <embed type='application/pdf' :src='file' frameborder="0" width="100%" height="800" />
</template>