/**
 * ⛔️ This file defines your pages routes.
 *   How to add a new page:
 *   1. Create a new .vue file inside the pages folder.
 *   2. Import it as you can see below.
 *   3. Add a new object item to the routes array defining the path, name and component.
 */

import { createRouter, createWebHistory } from "vue-router";
import Login from "@/pages/Login.vue";
import { useUuidStore } from "@stores/variables";
// product Setting
// import Package from "./src/pages/Package.vue";
// import AddPackage from "./src/pages/AddPackage.vue";
// import PackageDetail from "./src/pages/PackageDetail.vue";
import LayoutAuthenticated from "@/layouts/LayoutAuthenticated.vue";
//import AuthorizedClaim from "@/features/claim/pages/AuthorizedClaim.vue";
import PremiumReceipt from "@/features/finance/pages/PremiumReceipt.vue";
import EditQuotation from "@/features/quotation/components/form/EditQuotationForm.vue";
// import PremiumPaymentRequestVue from "@/features/quotation/pages/PremiumPaymentRequest.vue";
// import PremiumDebitAdvice from "@/features/finance/pages/PremiumDebitAdvice.vue";

const routes = [
  { path: "/login", name: "login", component: Login },
  {
    path: "/login",
    component: LayoutAuthenticated,
    children: [
      {
        path: "/",
        name: "dashboard",
        component: () => import("@/pages/Dashboard.vue"),
        meta: { breadcrumb: "Dashboard" },
      },
      {
        path: "/Quotation",
        meta: { breadcrumb: "Quotation" },
        redirect: "/Quotation/New-Quotation",
        children: [
          {
            path: "/Quotation/New-Quotation",
            name: "New Quotation",
            component: () =>
              import("@/features/quotation/pages/NewQuotation.vue"),
            meta: { breadcrumb: "New Quotation" },
          },
          {
            path: "/Quotation/Saved-Quotations",
            name: "Saved Quotations",
            component: () =>
              import("@/features/quotation/pages/SavedQuotations.vue"),
            meta: { breadcrumb: "Saved Quotations" },
            children: [],
          },
          {
            path: "/view_quotation/:id?/:quotationId?",
            component: EditQuotation,
            meta: { breadcrumb: "Quotation Detail" },
          },
          {
            path: "/Quotation/Issued-Quotations",
            name: "Issued Quotations",
            component: () =>
              import("@/features/quotation/pages/IssuedQuotations.vue"),
            meta: { breadcrumb: "Issued Quotations" },
          },
          {
            path: "/Quotation/Accepted-Quotations",
            name: "Accepted Quotations",
            component: () =>
              import("@/features/quotation/pages/AcceptedQuotations.vue"),
            meta: { breadcrumb: "Accepted Quotations" },
          },
        ],
      },
      {
        path: "/Underwriting",
        meta: { breadcrumb: "Underwriting" },
        redirect: "/Underwriting/New-Policy",
        children: [
          {
            path: "/Underwriting/Amend-Policy",
            component: () => import("@/pages/Institution.vue"),
            meta: { breadcrumb: "Amend Policy" },
          },
          {
            path: "/Underwriting/Issued-Policies",
            name: "issuedPolicies",
            component: () => import("@/pages/issuedPolicies.vue"),
            meta: { breadcrumb: "Issued Policies" },
          },
          {
            path: "/Underwriting/New-Policy",
            name: "quotedPolicies",
            component: () => import("@/pages/QuotedPolicies.vue"),
            meta: { breadcrumb: "New Policy" },
          },
          {
            path: "/Underwriting/Import-Policy",
            name: "addNewInstitutions",
            component: () => import("@/pages/addNewInstitution.vue"),
            meta: { breadcrumb: "Import POlicy" },
          },
        ],
      },
      {
        path: "/Authorization",
        meta: { breadcrumb: "Authorization" },
        redirect: "/Authorization/Requested",
        children: [
          {
            path: "/Authorization/Requested",
            name: "Requested Authorizations",
            component: () => import("@/pages/RequestedAuth.vue"),
            meta: { breadcrumb: "Requested Authorizations" },
          },
        ],
      },
      {
        path: "/ClaimManagement",
        meta: { breadcrumb: "Claim Management" },
        redirect: "/ClaimManagement/Cash-Claims",
        children: [
          {
            path: "/ClaimManagement/Cash-Claims",
            name: "cashClaims",
            component: () => import("@/pages/cash-claims.vue"),
            meta: { breadcrumb: "Cash Claims" },
          },
          {
            path: "/ClaimManagement/Credit-Services",
            name: "RegisterClaim",
            component: () => import("@/pages/notifiedClaims.vue"),
            meta: { breadcrumb: "Credit Services" },
          },
          {
            path: "/ClaimManagement/Process-Claims",
            name: "processClaim",
            component: () => import("@/pages/processClaim.vue"),
            meta: { breadcrumb: "Process Claim" },
          },
          {
            path: "/ClaimManagement/Processed-Claims",
            name: "processedClaim",
            component: () => import("@/pages/processedClaim.vue"),
            meta: { breadcrumb: "Processed Claims" },
          },
          {
            path: "/ClaimManagement/Checked-Claims",
            name: "checkedClaim",
            component: () => import("@/pages/checkedClaim.vue"),
            meta: { breadcrumb: "Checked Claims" },
          },
          {
            path: "/ClaimManagement/Approved-Claims",
            name: "AuditedClaims",
            component: () => import("@/pages/approvedClaim.vue"),
            meta: { breadcrumb: "Approved Claims" },
          },
          {
            path: "/ClaimManagement/Authorized-Claims",
            name: "AuthorizeViewClaims",
            component: () => import("@/pages/authorizeView.vue"),
            meta: { breadcrumb: "Completed Claims" },
          },
        ],
      },
      {
        path: "/finance",
        meta: { breadcrumb: "Finance" },
        redirect: "/Finance/Premium-Receipt",
        children: [
          {
            path: "/Finance/Premium-Receipt",
            name: "Premium Receipt",
            component: PremiumReceipt,
            meta: { breadcrumb: "Premium Receipt" },
          },
          {
            path: "/Finance/Claim-Payment",
            name: "AuthorizeClaims",
            component: () => import("@/pages/authorizeClaim.vue"),
            meta: { breadcrumb: "Claim Payment" },
          },
          {
            path: "/Finance/Paid-Claims",
            name: "paidClaims",
            component: () => import("@/pages/paidClaims.vue"),
            meta: { breadcrumb: "Paid Claims" },
          },
        ],
      },
      {
        path: "/provider-contracts",
        meta: { breadcrumb: "Provider Contracts" },
        redirect: "/ProviderContracts/Create-Contract",
        children: [
          {
            path: "/ProviderContracts/Create-Contract",
            name: "addNewContracts",
            component: () => import("@/pages/addNewPPcontract.vue"),
            meta: { breadcrumb: "New Contract" },
          },
          {
            path: "/ProviderContracts/Active-Contracts",
            name: "contracts",
            component: () => import("@/pages/Contracts.vue"),
            meta: { breadcrumb: "Active Contracts" },
          },
          {
            path: "/ProviderContracts/Suspended-Contracts",
            name: "suspendedContracts",
            component: () => import("@/pages/suspendedContracts.vue"),
            meta: { breadcrumb: "Suspended Contracts" },
          },
        ],
      },
      {
        path: "/provider-setting",
        meta: { breadcrumb: "Provider Settings" },
        redirect: "/ProviderSetting/Add-Provider",
        children: [
          {
            path: "/ProviderSetting/Add-Provider",
            name: "addProviders",
            component: () => import("@/pages/addProviders.vue"),
            meta: { breadcrumb: "Add Provider" },
          },
          {
            path: "/ProviderSetting/Active-Providers",
            component: () => import("@/pages/providers.vue"),
            meta: { breadcrumb: "Active Providers" },
          },
          {
            path: "/ProviderSetting/Inactive-Providers",
            name: "inActiveProviders",
            component: () => import("@/pages/inActiveProviders.vue"),
            meta: { breadcrumb: "Inactive Providers" },
          },
        ],
      },
      {
        path: "product-settings",
        meta: { breadcrumb: "Product Setting" },
        redirect: "/ProductSetting/Coverages",
        children: [
          {
            path: "/ProductSetting/Coverages",
            name: "package",
            component: () => import("@/pages/Package.vue"),
            meta: { breadcrumb: "Coverages" },
          },
        ],
      },
      {
        path: "/reports",
        meta: { breadcrumb: "Reports" },
        redirect: "clientReport",
        children: [
          {
            name: "clientReport",
            path: "/report/provider",
            component: () => import("@/components/reports/providerReport.vue"),
            meta: { breadcrumb: "Provider Report" },
          },
          {
            name: "providerReport",
            path: "/report/client",
            component: () => import("@/components/reports/ClientReport.vue"),
            meta: { breadcrumb: "Client Report" },
          },
          {
            name: "branchReport",
            path: "/report/branch",
            component: () => import("@/components/reports/branchReport.vue"),
            meta: { breadcrumb: "Branch Report" },
          },
        ],
      },

      {
        path: "/institution-contracts/:Uuid/:status",
        name: "institution-contracts",
        meta: { breadcrumb: "Policy Details" },
        component: () => import("@/pages/institutionContracts.vue"),
        beforeEnter: (to, from, next) => {
          const uuidStore = useUuidStore();
          if (to.params.Uuid === uuidStore.Uuid) {
            next();
          } else {
            next({
              name: "file-not-found",
              // params: { pathMatch: to.path.split("/").slice(1) },
              // query: to.query,
              // hash: to.hash,
            });
          }
        },
      },

      {
        path: "/package/packagedetail/:id",
        name: "packagedetail",
        component: () => import("@/pages/PackageDetail.vue"),
        meta: { breadcrumb: "Package Detail" },
      },
      {
        path: "/package/addpackage/:id",
        name: "addpackage",
        component: () => import("@/pages/AddPackage.vue"),
        meta: { breadcrumb: "Add Package" },
      },
      {
        path: "/insured-persons",
        name: "insured-persons",
        component: () => import("@/pages/InsuredPersons.vue"),
        meta: { breadcrumb: "Insured Persons" },
      },

      {
        path: "/provider/contracts/:Uuid",
        breadCrumb: "/Provider Contracts",
        name: "providerContracts",
        component: () => import("@/pages/providersContract.vue"),
      },
      {
        path: "/contract-services/:providerId/:payerProviderContractUuid",
        name: "contractServices",
        component: () => import("@/pages/EligibleContractServices.vue"),
        meta: { breadcrumb: "Provider Services" },
      },

      {
        path: "/create_quotation/:id?",
        component: () =>
          import("@/features/quotation/components/form/AddQuotationForm.vue"),
        meta: { breadcrumb: "Create Quotation" },
      },
      {
        path: "/insured_and_dependants/:id",
        component: () =>
          import("@/features/quotation/pages/InsuredAndDependants.vue"),
      },
      {
        path: "/policy_debit_note/:institutionUuid/:quotationUuid/:payerInstitutionContractUuid",
        component: () =>
          import("@/features/quotation/pages/PolicyDebitNote.vue"),
      },
      // {
      //   path: "/premium_payment_request/:id?/:quotationId?",
      //   component: () =>
      //     import("@/features/quotation/pages/premium_payment_request.vue"),
      // },
      {
        path: "/premium_debit_advice/:id?/:quotationId?/:payerInstitutionContractUuid?",
        component: () =>
          import("@/features/finance/pages/PremiumDebitAdvice.vue"),
      },

      {
        path: "/quotation_sample/:id?/:quotationId?",
        component: () =>
          import("@/features/quotation/pages/QuotationSample.vue"),
      },
      {
        path: "/contract-services/addProviders",
        component: () => import("@/pages/addProviders.vue"),
        meta: { breadcrumb: "Add Providers" },
      },
      {
        path: "/institutions",
        name: "institutions",
        component: () => import("@/pages/Institution.vue"),
      },
      {
        path: "/providers",
        name: "Providers",
        component: () => import("@/pages/providers.vue"),
      },
      {
        path: "/assign-providers/:Uuid/:id",
        name: "ProvidersInContract",
        component: () =>
          import("@/components/providers/providersInContract.vue"),
      },
      {
        path: "/assign-providers/:Uuid",
        name: "addProvidersToContract",
        component: () =>
          import("@/components/providers/addActiveProviderToContract.vue"),
      },
      {
        path: "/policy",
        name: "Policy",
        component: () => import("@/components/contract/policyWording.vue"),
      },
      {
        path: "/claims/cpo/:id/:Uuid/:status",
        name: "CPO",
        component: () => import("@/components/attachments/CPO.vue"),
      },

      {
        path: "/claims/VoucherSlip/:bCode",
        name: "paymentVoucherSlip",
        component: () => import("@/components/claims/paymentVoucher.vue"),
        meta: { breadcrumb: "Payment Voucher Slip" },
      },
      {
        path: "/claims/payment/requestSlip/:bCode/:Uuid",
        name: "paymentSlip",
        component: () => import("@/components/claims/paymentRequestSlip.vue"),
        meta: { breadcrumb: "Payment Slip" },
      },
      {
        path: "/users",
        name: "users",
        component: () => import("@/pages/Users.vue"),
      },
      {
        path: "/roles-and-privileges",
        name: "privileges",
        component: () => import("@/pages/Privileges.vue"),
      },
      {
        path: "/contract-services/:payerProviderContractUuid/addInstitutions",
        name: "addInstitutions",
        component: () => import("@/pages/AddInstitutions.vue"),
      },
      {
        path: "/institution/contract/:Uuid",
        name: "add-institution-contracts",
        component: () => import("@/pages/addInstitutionContract.vue"),
      },
      {
        path: "/contract-institutions/:payerProviderContractUuid/:payerProviderContractCode",
        name: "contractInstitutions",
        component: () => import("@/pages/ContractInstitutions.vue"),
        props: true,
      },
      {
        path: "/claims",
        name: "claims",
        component: () => import("@/pages/claims.vue"),
      },

      {
        path: "/claims/:id/:Uuid/:status",
        name: "claim-batch-details",
        component: () => import("@/components/claims/claim-batch-detail.vue"),
        meta: { breadcrumb: "Claim Details" },
      },
      {
        path: "/paid/claims/:id/:Uuid",
        name: "paid-batch-details",
        component: () => import("@/components/claims/paid-batch-detail.vue"),
        meta: { breadcrumb: "Paid Claim Details" },
      },
      {
        path: "/claims/view/:id/:Uuid/:status",
        name: "claim-batch-view",
        component: () => import("@/components/claims/claim-batch-view.vue"),
      },
      {
        path: "/paid/claims/view/:id",
        name: "paidClaimView",
        component: () => import("@/components/claims/paidClaimDetails.vue"),
      },

      {
        name: "claim-detail",
        path: "/claim-detail/:id",
        component: () => import("@/pages/claim-detail.vue"),
        props: true,
      },
      {
        name: "claim-detail-page",
        path: "/claim-detail/:id/:Uuid/:caId/:status",
        component: () => import("@/pages/claim-detail-page.vue"),
        props: true,
      },
      {
        name: "assign-insured-person",
        path: "/assign-insured-person/:id/:Uuid",
        component: () => import("@/pages/assign-insured-person.vue"),
        props: true,
      },
      {
        name: "assign-eligible-service",
        path: "/assign-eligible-service/:id/:Uuid",
        component: () => import("@/pages/assign-eligible-service.vue"),
        props: true,
      },
    ],
  },
  {
    path: "/:catchAll(.*)*",
    name: "file-not-found",
    component: () => import("@/pages/404.vue"), // Your 404 component
  },
];

const history = createWebHistory();

const router = createRouter({
  history,
  routes,
});

export default router;
