<script setup>
import { mdiFilePdfBox, mdiPlus } from '@mdi/js';
import QuotationPage from '@/features/quotation/pages/QuotationPage.vue';
import SavedQuotationsDataProvider from '@/features/quotation/components/SavedQuotationsDataProvider.vue';
import TableWrapper from '@/components/TableWrapper.vue';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { QuotationStatus } from '@/util/enums';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { payQuotation } from '@/features/quotation/api/quotationApi'
import { useApiRequest } from '@/composables/useApiRequest'
import { openModal } from '@/modals';
import { toast } from '@/util/utils';
import { unpaidQuotaionStore } from '@/features/finance/store/unpaidQuotationStore'

const payReq = useApiRequest()

const unpaidStore = unpaidQuotaionStore()

function pay(institution) {
  openModal('PayPremium', {
    totalPremium: institution?.totalPremium,
    totalSumInsured: institution?.totalSumInsured
  }, data => {
    if(data) {
      openModal('Confirmation', {
        title: 'Premium Payment',
        message: 'Are you sure you want to pay the premium?'
      }, res => {
        if(res) {
          payReq.send(
            () => payQuotation(institution.quotationUuid, data),
            res => {
              toast(res.success, 'Paid Successfully', 'Couldnt Pay. Try Again.')
              if(res.success) {
                unpaidStore.remove(institution.institutionUuid)
              }
            }
          )
        }
      })
    }
  })
}
</script>
<template>
  <QuotationPage :headers="{
    head: ['quotation number', 'created date'],
    row: ['quotationCode', 'createdDate']
  }" :store="unpaidStore" :status="QuotationStatus.UNPAID" :DataProvider="SavedQuotationsDataProvider" class="flex flex-col" :icon="mdiPlus">
    <template #actions="{row}">
      <DropdownBtn @click="$router.push('/view_quotation/' + row?.institutionUuid + '/' + row?.quotationUuid)">
        View Quotation
      </DropdownBtn>
    </template>
    <template #additionalBtns="{row}">
      <button @click="$router.push(`/premium_debit_advice/${row.institutionUuid}/${row?.quotationUuid}/${row?.payerInstitutionContractUuid}`)" class="w-12 h-10 flex items-center justify-center rounded-md border border-gray-300">
        <BaseIcon :path="mdiFilePdfBox" :size="25" />
      </button>
      <button @click="pay(row)" class="bg-lime-500 text-white w-12 h-10 flex items-center justify-center rounded-md border border-gray-300">
        Pay
      </button>
    </template>
  </QuotationPage>
</template>