module.exports = {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  darkMode: "class",

  theme: {
    screens: {
      xxs: "380px",
      // => @media (min-width: 380px) { ... }

      xs: "512px",
      // => @media (min-width: 512px) { ... }

      sm: "640px",
      // => @media (min-width: 640px) { ... }

      md: "768px",
      // => @media (min-width: 768px) { ... }

      lg: "1024px",
      // => @media (min-width: 1024px) { ... }

      xl: "1280px",
      // => @media (min-width: 1280px) { ... }

      "2xl": "1536px",
      // => @media (min-width: 1536px) { ... }

      "3xl": "1600px",
      // => @media (min-width: 1600px) { ... }

      "4xl": "1920px",
      // => @media (min-width: 1920px) { ... }
    },
    fontFamily: {
      poppins: ["DM sans", "sans-serif"],
    },
    extend: {
      colors: {
        primary: "var(--color-primary)",
        secondary: "var(--color-secondary)",
        "section-bg": "var(--color-bgSec)",
        "sidebarText-active": "var(--color-sidebar)",
        sidebarText: "var(--border-color)",
        headline: "#454545",
        headerText: "#3F3851",
        LoginBackground: "var(--color-login)",
        TextPrimary: "#050B1D",
        borderColor: "var(--border-color)",
      },
      fontSize: {
        small: ["0.875rem", { lineHeight: "1.25rem" }],
        sidebarActive: ["1rem", { lineHeight: "1.575rem" }],
        sidebar: ["1rem", { lineHeight: "1.4rem" }],
        medium: ["1.25rem", { lineHeight: "1.75rem" }],
        large: ["1.875rem", { lineHeight: "2.25rem" }],
      },
      fontWeight: {
        XtraSmall: "100",
        smaller: "200",
        small: "300",
        medium: "400",
        large: "500",
        larger: "600",
        largest: "700",
        header: "800",
        "header-large": "900",
      },
    },
  },
  plugins: [require("@tailwindcss/forms")],
};
