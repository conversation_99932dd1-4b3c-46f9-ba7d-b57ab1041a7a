import { RouterLink } from 'vue-router';
<template>
  <!--
    This example requires updating your template:

    ```
    <html class="h-full">
    <body class="h-full">
    ```
  -->
  <main class="items-center justify-center min-h-screen place-items-center bg-white px-6 py-24 sm:py-32 lg:px-8 flex">
    <div class="text-center">
      <p class="text-base font-semibold text-indigo-600">404</p>
      <h1 class="mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl">
        Page not found
      </h1>
      <p class="mt-6 text-base leading-7 text-gray-600">
        Sorry, we couldn’t find the page you’re looking for. click here
        <span class="text-primary text-xl px-2"><router-link to="/">Home</router-link></span>
      </p>
      <!-- <div class="mt-10 flex items-center justify-center gap-x-6">
        <a
          class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-xl font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >Go back home</a
        >
        <a href="#" class="text-xl font-semibold text-gray-900"
          >Contact support <span aria-hidden="true">&rarr;</span></a
        >
      </div> -->
    </div>
  </main>
</template>
