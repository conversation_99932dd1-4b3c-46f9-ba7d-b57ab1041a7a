import api from "@/scripts/api";
import { handleError } from "vue";
import axios from "axios";
import { useToast } from "vue-toastification";
const toast = useToast();
export default {
  async getAllPackages() {
    try {
      const response = await api.get("/package");
      return response;
    } catch (error) {
      toast.error("Oops! some thing error");

      return handleError(error);
    }
  },

  async addPackage(data) {
    console.log("submitted data", data);
    let pdata = {};
    try {
      await api.post("/package", data).then((response) => {
        toast.success("package created successefuly");
        pdata = response;
      });

      return pdata;
    } catch (error) {
      toast.error("Oops! some thing eror", error);
      return handleError(error);
    }
  },

  async updatePackage(data, id) {
    let rdata = [];
    try {
      const response = await api
        .put(`/package/${id}`, data)
        .then((response) => {
          rdata = response;
          console.log("response iss from service", response);
          toast.success("package updated successefuly");
        });

      return rdata;
    } catch (error) {
      toast.error("Oops! some thing error");
      return handleError(error);
    }
  },

  async getPackageById(id) {
    let rep = [];
    try {
      await api.get(`/api/medicalinsurance/package/${id}`).then((response) => {
        rep = response;
        console.log("response by id from service", response);
      });

      return rep;
    } catch (error) {
      return handleError(error);
    }
  },

  async deletePackageId(packageUuid) {
    try {
      const response = await api.delete(`/package/${packageUuid}`);
      toast.success("deleted successfully");
      return true;
    } catch (error) {}
  },
};
