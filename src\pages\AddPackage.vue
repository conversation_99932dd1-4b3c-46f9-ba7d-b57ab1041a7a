<script setup>
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import { ref,onMounted,watch } from 'vue';
import { storeToRefs } from 'pinia'
import {usePackageStore} from '../../stores/packageStore'
import {useRatePerpersonStore} from '../../stores/packageRatePerpersonStore'
import {useRatePerFamiliyStore} from '../../stores/packageRatePerfamiliyStore'

import { useRoute } from 'vue-router';

const route = useRoute();
const packageStore=usePackageStore()
const ratePerpersonStore=useRatePerpersonStore()
const ratePerfamiliyStore=useRatePerFamiliyStore()
const { packageList, PackageRange} = storeToRefs(packageStore);

const props=defineProps({id:{type:Number}})
const  packageName=ref(route.query.packageName)
const packageUuid = ref(route.params.id);
const error=ref({'name':'','minLimit':'','maxiLimit':'',})
const perPersonForm=ref({'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','rate':'','status':'ACTIVE'})
const perFamiliyForm=ref({'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','minLimit':'','rate':'','status':'ACTIVE','familySize':''})

const perPersonInput=ref([{'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','rate':'','status':'ACTIVE'}])
const familiyPerInput=ref([{'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','minLimit':'','rate':'','status':'ACTIVE','familySize':''}])
let  temPackages=ref([])



const option=ref('perfamiliy')
const addForm=()=> {
     packageStore.addPackageRange(perPersonForm.value)
     perPersonForm.value=ref({'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','rate':'','status':'ACTIVE'})

    }

const add=()=>{
  console.log('ccccc',perPersonForm.value)
  packageStore.addPackageRange(perPersonForm.value)
}

 const removeRange=(index)=>{
          console.log('index is',index)
         packageStore.deletePackageRange(index)  
    
         }

  const saveToDBPerPerson=async ()=> {
   const data={
  "requestBody": perPersonInput.value}
     for(let i=0;i<perPersonInput.value.length;i++) {

          if(perPersonInput.value[i].minLimit > perPersonInput.value[i].maxLimit) {
           
             alert('Minimum limit must not greater than Maximum Limit')
             return

          }
             }


       

  
   await ratePerpersonStore.addPackageRatePerperson(data)

  }    
  const saveToDBPerFamiliy=async ()=> {
    const data={
  "requestBody": familiyPerInput.value}
    console.log('data adata data',data)

    for(let i=0;i<familiyPerInput.value.length;i++) {

if(familiyPerInput.value[i].minLimit > familiyPerInput.value[i].maxLimit) {
 
   alert('Minimum limit must not greater than Maximum Limit')
   return

}
   }


    await ratePerfamiliyStore.addPackageRatePerFamiliy(data)

  }   
  

const addPersonTable=()=> 
        {
         console.log('you clicked me nn')
        perPersonInput.value.push({'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','rate':'','status':'ACTIVE'})

        }
        const addPersonTablePressEnter=(index)=> 
        {
         
           if(index+1===perPersonInput.value.length)
            {
              perPersonInput.value.push({'packageUuid':packageUuid.value,'minLimit':'','maxLimit':'','rate':'','status':'ACTIVE'})
            }

        }

        const addFmiliyTablePressEnter=(index)=> 
        {
         
           if(index+1===familiyPerInput.value.length)
            {
              familiyPerInput.value.push({'packageUuid':packageUuid.value,'maxLimit':'','minLimit':'','rate':'','status':'ACTIVE','familySize':''})
            }

        }




const addFamilyTable=()=> {

       familiyPerInput.value.push({'packageUuid':packageUuid.value,'maxLimit':'','minLimit':'','rate':'','status':'ACTIVE','familySize':''})


             }
const removePerPerson=(index)=>{
         perPersonInput.value.splice(index,1)
  
         }
 const removePerFamiliy=(index)=>{
  familiyPerInput.value.splice(index,1)
  
         }


// onMounted(() => {
//   //  feaching from API
       
// });

watch(PackageRange.value,()=> {
     console.log('sssss',PackageRange.value)
     temPackages.value=PackageRange.value

  })



</script>
<template>
      <div class="ml-[10px]  min-h-[500px] mt-4 ">
      <div class="bg-white p-2 rounded-lg shadow-md mb-6">

        <div class="flex justify-between items-center mb-4">
          <h1 class="text-2xl font-semibold">Add Rates for <span class="text-primary">{{ packageName }}</span></h1>
          <router-link to="/package">
            <a class="flex items-center text-primary hover:text-gray-700">
              <svg class="w-4 h-4 mr-2" xmlns="https://icons8.com/icon/63650/plus" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
              Back
            </a>
          </router-link>
        </div>

        <div class="flex justify-evenly space-x-3">
          <div class="bg-gray-100 p-4 rounded-md">
            <div class="flex items-center justify-between mb-4">
              <p class="text-lg font-semibold">For Individual</p>
              <button @click.prevent="addPersonTable" class=" bg-indigo-400 text-white rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48">
                  <path fill="#fff" d="M21,14h6v20h-6V14z"></path>
                  <path fill="#fff" d="M14,21h20v6H14V21z"></path>
                </svg>
              </button>
            </div>

            <table class="w-full">
              <!-- ... Your table header ... -->
              <thead class="bg-gray-50">
                
                <tr>
                  <th
                    scope="col"
                    class="border text-left text-sm  text-gray-600 px-1 py-1"
                  >
                    Min limit
                  </th>
                  
                  <th
                    scope="col"
                    class="border text-left text-sm  text-gray-600 px-1 py-1"
                  >
                    Max limit
                  </th>
                  <th
                    scope="col"
                    class="border text-left text-sm  text-gray-600 px-1 py-1"
                  >
                    Rate(%)
                  </th>   
                </tr>
              </thead>

              <tbody class="">
                <tr v-for="(packageRange, index) in perPersonInput" :key="index">
                  <!-- ... Your table row content ... -->
                  <td class="border w-20"><input  type="number" v-model="packageRange.minLimit" class="w-[120px] h-5 m-0 border-white focus:border-white"  id="name" /> </td>
                    <td class="border w-20"><input type="number" v-model="packageRange.maxLimit" class="w-[120px] h-5 m-0 border-white" id="name" /></td>   
                    <td class="border w-10"><input type="number" @keypress="addPersonTablePressEnter(index)" v-model="packageRange.rate" class="w-[110px] h-2 m-0 border-white focus:border-white"  id="name" /> </td>
                  <td class="border px-2 py-1">
                    <button v-if="index > 0" @click.prevent="removePerPerson(index)" class="font-bold">
                      -
                    </button>
                  </td>
                  
                </tr>
              </tbody>

              <!-- ... Your table footer and buttons ... -->
            </table>

            <button @click="saveToDBPerPerson" class="mt-4 w-full bg-primary text-white rounded-md py-1 px-1 hover:bg-indigo-500">
              Save
            </button>
          </div>
          <!-- For Family -->
          <div class="bg-gray-100 p-4 rounded-md">
             
             <div class="flex items-center justify-between mb-4">
               <p class="text-lg font-semibold">For Family</p>
               <button @click.prevent="addFamilyTable" class=" bg-indigo-400 text-white rounded-md">
                 <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48">
                   <path fill="#fff" d="M21,14h6v20h-6V14z"></path>
                   <path fill="#fff" d="M14,21h20v6H14V21z"></path>
                 </svg>
               </button>
             </div>
         <!-- table -->
 
           <table class="w-full">
               <!-- ... Your table header ... -->
               <thead class="bg-gray-50">
                 
                 <tr>
        <th
          scope="col"
          class="border text-left text-sm  text-gray-600 px-1 py-1"
        >
          Min limit
        </th>
        
        <th
          scope="col"
          class="border text-left text-sm  text-gray-600 px-1 py-1"
        >
          Max limit
        </th>
        <th
          scope="col"
          class="border text-left text-sm text-gray-600 px-1 py-1 px-1 py-1"
        >
          Familiy Size
        </th>
        <th
          scope="col"
          class="border text-left text-sm  text-gray-600 px-1 py-1"
        >
          Rate(%)
        </th>  
        
         
    
        
      </tr>
    </thead>
 
 
    <tbody>
     <tr v-for="(packageRange,index) in familiyPerInput">
     <td class="border w-20"><input v-model="packageRange.minLimit" class="w-[102px] h-5 m-0 border-white focus:border-white" type="number" id="name" /> </td>
     <td class="border w-20"><input v-model="packageRange.maxLimit" class="w-[103px] h-5 m-0 border-white" type="number" id="name" /></td>   
     <td class="border w-20"><input v-model="packageRange.familySize" class="w-[103px] h-5 m-0 border-white focus:border-white" type="number" id="name" /> </td>
     <td class="border w-20"><input @keypress="addFmiliyTablePressEnter(index)" v-model="packageRange.rate" class="w-[103px] h-5 m-0 border-white focus:border-white" type="number" id="name" /> </td>
     <td class="border px-2 py-1">
                    <button v-if="index > 0" @click.prevent="removePerFamiliy(index)" class="font-bold">
                      -
                    </button>
                  </td>
   
    </tr>
    </tbody>
    
 
               <!-- ... Your table footer and buttons ... -->
             </table>
 
             <button @click="saveToDBPerFamiliy" class="mt-4 w-full bg-primary text-white rounded-md py-1 px-1 hover:bg-indigo-500">
               Save
             </button>
 
 
           </div>



        </div>












        
        </div>
        </div>
 
</template>


