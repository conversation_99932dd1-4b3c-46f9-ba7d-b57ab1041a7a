<script setup>
import { mdiAlertRemove } from "@mdi/js";
const emit = defineEmits(["confirmed", "canceled"]);

const deleteConfirmed = () => {
  emit("confirmed");
};


</script>

<template>
  <div>
    

    <div>
      <div className="bg-white    sm:p-6 sm:pb-4">
        <div className="sm:flex sm:items-start">
          <div 
            className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"
          >
            <!-- <svg className="h-6 w-6 text-red-600" aria-hidden="true" /> -->

             <button >
              <svg 
              viewBox="0 0 24 24"
              :width="24"
              :height="iconSize"
              class="inline-block"
            >
              <path fill="currentColor" :d="mdiAlertRemove" />
            </svg>
             </button>
          </div>
          <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
            <!-- title here -->
            <span
              as="h3"
              className="text-base font-semibold leading-6 text-gray-900"
            >
            </span>
            <!-- title here -->

            <div className="mt-2">
              <p className="text-xl text-gray-500">
                Are you sure you want to delete?
              <br> 
                <span>Your data will be lost permanently.</span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6 ">
        <button
          type="button"
          @click="deleteConfirmed"
          className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-xl font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
        >
          Delete
        </button>
        <button
          type="button"
          @click="emit('canceled')"
          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-xl font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
          ref="{cancelButtonRef}"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>
