<script setup>
import * as session from '@/scripts/session';
import { useRouter } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
const router = useRouter();

const logout = () => {
  session.setUserIsLoggedIn(false);
  router.push('/login');
};
</script>
<template>
    <SectionMain>
      <div class="">Roles And Privileges</div>
    </SectionMain>
</template>
