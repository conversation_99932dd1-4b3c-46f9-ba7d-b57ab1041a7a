<script setup>
import { useApiRequest } from '@/composables/useApiRequest'
import { getInstitutionsId }  from '@/features/quotation/api/institutionApi'
import { getQuotationById } from '@/features/quotation/api/quotationApi'
import { getInsuredWithDependants } from '@/features/quotation/api/insuredApi'
import { getPayerInstitutionContract } from '../api/payerInstitutionContract'
import { getAllPackages } from '@/features/quotation/api/packageApi'
import { useRoute } from 'vue-router';

const req = useApiRequest()

const route = useRoute()

const institutionUuid = route.params?.institutionUuid
const quotationUuid = route.params?.quotationUuid
const payerInstitutionContractUuid = route.params?.payerInstitutionContractUuid

async function sendAll() {
  try {
    const res = await Promise.all([getInstitutionsId(institutionUuid), getQuotationById(quotationUuid), getInsuredWithDependants(payerInstitutionContractUuid), getPayerInstitutionContract(payerInstitutionContractUuid), getAllPackages()])
    return {
      success: true,
      data: {
        institution: res?.[0]?.data,
        quotation: res?.[1]?.data,
        members: res?.[2]?.data,
        contract: res?.[3]?.data,
        packages: res?.[4]?.data
      },
    }
  } catch(err) {
    return {
      success: false,
      data: '',
      error: err.message
    }
  }
}

req.send(
  () => sendAll()
)
</script>

<template>
  <slot
    :institution="req.response.value?.institution"
    :quotation="req.response.value?.quotation"
    :members="req.response.value?.members"
    :contract="req.response.value?.contract"
    :packages="req.response.value?.packages"
    :pending="req.pending.value"
  />
</template>