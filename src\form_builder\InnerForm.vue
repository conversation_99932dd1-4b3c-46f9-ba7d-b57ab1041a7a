<script setup>
import { ref, provide, nextTick, reactive, watch, inject } from "vue";

const props = defineProps({
  name: String
})

const form = ref();

const valid = ref(false);
const id = "form_" + Math.floor(Math.random() * 10000);

const setValue = inject("setValue");
const reset = inject("reset");

provide("setValue", setInnerValueValue);
provide("id", id);

const values = []
function setInnerValueValue(name, value, valid) {
  const idx = values.findIndex(el => el.name == name)
  if (idx == -1) {
    values.push({
      name,
      value,
      valid
    })
  } else {
    values[idx] = {
      name,
      value,
      valid
    }
  }

  let allValid = values.every(el => el.valid == true)
  setValue(props.name, values.reduce((state, el) => {
    state[el.name] = el.value
    return state
  }, {}), allValid)
}

function submit(fun = (f) => f) {
  validateAll.validate = true;

  nextTick(() => {
    if (!form.value) return false;
    
    let allValid = values.every(el => el.valid == true)
    
    if (allValid) {
      fun({ values: values.reduce((state, el) => {
        state[el.name] = el.value
        return state
      }, {}), reset });
    }

    valid.value = allValid;
    validateAll.validate = false;
    validateAll.first = false;
  });
}
</script>
<template>
  <form autocomplete="off" :id="id" ref="form" action="">
    <slot :valid="valid" :submit="submit"></slot>
  </form>
</template>
