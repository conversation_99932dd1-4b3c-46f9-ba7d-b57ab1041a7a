<script setup>
import pdfMake from 'pdfmake/build/pdfmake'
import pdfFonts from 'pdfmake/build/vfs_fonts'
import { toWords, getBgbase64 } from '@/util/utils'
import { computed, ref } from 'vue';
import { formatCurrency, formatNumber, secondDateFormat } from '@/util/utils'
import { IndividualTypes, PlanTypes } from '@/util/enums';

pdfMake.vfs = pdfFonts.pdfMake.vfs

const props = defineProps({
  packages: {
    type: Array,
    required: true
  },
  institution: {
    type: Object,
    required: true
  },
  quotation: {
    type: Object,
    required: true
  }
})

const femaleGroup = Object.groupBy(props.packages, ({ gender }) => gender == 'FEMALE')
const planGroup = Object.groupBy(props.quotation.quoatedServices, ({ planType }) => planType)

const femaleonlyPackages = femaleGroup.true.map(el => el.packageUuid)

const individualPlan = (planGroup?.[PlanTypes.Individual] || []).filter(el => !femaleonlyPackages.includes(el.packageUuid))
const individualPlanPerPackage = individualPlan.reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: el.coverage * el.numberOfInsured,
      type: [el]
    })
  }
  return state
}, [])

const sharedlPlanPerPackage = (planGroup?.[PlanTypes.Shared] || []).reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += el.coverage * el.numberOfInsured * el.description
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: el.coverage * el.numberOfInsured * el.description,
      type: [el]
    })
  }
  return state
}, [])

const familyPlanPerPackage = (planGroup?.[PlanTypes.Family] || []).reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: (el.coverage * el.numberOfInsured),
      type: [el]
    })
  }
  return state
}, [])

const femalePlan = Object.values(planGroup).flat().filter(el => femaleonlyPackages.includes(el.packageUuid))
const femalePlanPerPackage = femalePlan.reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: (el.coverage * el.numberOfInsured),
      type: [el]
    })
  }
  return state
}, [])

console.log(femalePlanPerPackage)
function getPackageName(id) {
  return props.packages.find(el => el.packageUuid == id)?.packageName
}

// function numberOfInsured() {
//   const group = Object.groupBy(props.quotation.quoatedServices, (({packageUuid}) => packageUuid))
//   return group[Object.keys(group)[0]].reduce((sum, el) => sum += el.numberOfInsured ,0)
// }

function numberOfInsured(data) {
  const type = data?.[0]?.planType
  const id = data?.[0]?.packageUuid

  if (femaleonlyPackages.includes(id)) {
    return data.find(el => !el.spouse).numberOfInsured
  } else if (type == PlanTypes.Family) {
    return data.reduce((sum, el) => sum += el.numberOfInsured, 0)
  } else if (type == PlanTypes.Individual) {
    return data.filter(el => el.individualType == IndividualTypes.Member).reduce((sum, el) => sum += el.numberOfInsured, 0)
  }
}

function numberOfDependants(data) {
  const type = data?.[0]?.planType
  const id = data?.[0]?.packageUuid

  if (femaleonlyPackages.includes(id)) {
    return data.find(el => el.spouse).numberOfInsured
  } else if (type == PlanTypes.Family) {
    return data.reduce((sum, el) => sum += (el.numberOfInsured * el.description), 0) - numberOfInsured(data)
  } else if (type == PlanTypes.Individual) {
    return data.filter(el => el.individualType != IndividualTypes.Member).reduce((sum, el) => sum += el.numberOfInsured, 0)
  }
}

function getAllNumberOfInsured() {
  return Object.values(
    Object.groupBy(props.quotation.quoatedServices, (({ packageUuid }) => packageUuid))
  )
    .reduce((sum, el) => {
      return sum += numberOfInsured(el)
    }, 0)
}

function getAllNumberOfDependants() {
  return Object.values(
    Object.groupBy(props.quotation.quoatedServices, (({ packageUuid }) => packageUuid))
  )
    .reduce((sum, el) => {
      return sum += numberOfDependants(el)
    }, 0)
}


const file = ref()
async function getPdf() {
  const bg = await getBgbase64('/crtificate_header_anbessa.png')

  const docDefinition = {
    content: [
      {
        style: ['defaultTopMargin', { fontSize: 12 }],
        alignment: 'right',
        stack: [
          {
            style: { bold: true },
            text: `Accepted Date: ${secondDateFormat(props.quotation?.acceptedDate)}`,
          },
          {
            text: [
              {
                text: `Quotation Number: `,
              },
              {
                style: { bold: true },
                text: props.quotation?.quotationCode
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        style: { bold: true, fontSize: 13 },
        text: `To ${props.institution.institutionName}`
      },
      {
        style: { decoration: 'underline' },
        text: props.institution.state
      },
      {
        marginTop: 8,
        style: { fontSize: 14, bold: true },
        text: 'Dear Sir/Madam'
      },
      {
        alignment: 'center',
        marginTop: 8,
        style: { fontSize: 14 },
        text: [
          {
            text: 'Subject: '
          },
          {
            style: { decoration: 'underline', bold: true },
            text: 'Acceptance Note & Initial PremiumPayment Request'
          }
        ]
      },
      {
        marginTop: 8,
        text: [
          'We are very glad to hear that our proposal under quotation no. ',
          { bold: true, text: props.quotation?.quotationCode },
          ' as got acceptance to provide Group Yearly Renewable Term (GYRT) with Comprhensive Accident Indemnity (CAI) and Group Medical Insurance coverage to your employees and dependents'
        ],
      },
      {
        marginTop: 8,
        text: 'Accordiungly   as   per   the   updated   employee   information   you   have   provided,   kindly   summurized hereunder our initial premium for your settlement'
      },
      {
        marginTop: 8,
        table: {
          widths: [20, 'auto', 'auto', '*', 'auto', 'auto'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Coverage', style: 'tableHeader' }, { text: '# Insured', style: 'tableHeader' }, { alignment: 'right', text: 'Sum Insured', style: 'tableHeader' }, { text: 'Plan Type', style: 'tableHeader' }, { alignment: 'right', text: 'Premium (ETB)', style: 'tableHeader' }],
            ...Object.values(
              props.quotation?.quoatedServices?.reduce((state, el, idx) => {
                if (state[el.packageUuid]) {
                  state[el.packageUuid].push(el)
                } else {
                  state[el.packageUuid] = [el]
                }
                return state
              }, {})
            )
              .reduce((state, el, idx) => {
                const row = []
                row.push(idx + 1)
                row.push({ fontSize: 12, text: props.packages.find(pack => pack.packageUuid == el?.[0]?.packageUuid)?.packageName })
                row.push(`M = ${numberOfInsured(el)},\nD = ${numberOfDependants(el)}`)
                row.push({ alignment: 'right', text: formatCurrency(el?.[0]?.coverage) })
                row.push(el?.[0]?.planType?.replace(/_/g, ' ') || '')
                row.push({ alignment: 'right', style: { bold: true }, text: formatCurrency(el.reduce((sum, el) => sum += el.premium, 0)) })
                state.push(row)
                return state
              }, []),
            [{ colSpan: 5, alignment: 'center', text: 'Revenue Stamp' }, {}, {}, {}, {}, { style: { bold: true }, alignment: 'right', text: formatCurrency(5) }],
            [{ colSpan: 5, alignment: 'center', text: 'Grand Total' }, {}, {}, {}, {}, { style: { bold: true, fontSize: 14 }, alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0) + 5) }],
          ]
        }
      },
      {
        marginTop: 8,
        text: [
          'This is therfore kindly request you to settle the initial premium of',
          ` Birr ${formatNumber(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)}`,
          {
            bold: true,
            text: ` (${toWords(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)})`
          },
          ' and Revenue Stamp Birr 5.00 (Five Birr)  at your earliest convinence to the following bank account'
        ]
      },
      {
        marginTop: 8,
        text: 'Bank Details', bold: true, fontSize: 15,
      },
      {
        text: `
              - Bank Name  Dashen Bank S.C.
              - Branch Name  Mickyleland Branch
              - Account Name NISCO for Life and Health
              - Account Number *************
        `
      },
      {
        marginTop: 8,
        text: 'Finally, we would like to thank you for giving us the chance to serve you and assure you of our usual and prompt service in the contract period.'
      },
      {
        marginTop: 20,
        style: { bold: true },
        text: 'Sincerely Yours,',
        pageBreak: 'after'
      },
      {
        bold: true,
        fontSize: 30,
        decoration: 'underline',
        alignment: 'center',
        text: 'Premium Debit Advice'
      },
      {
        marginTop: 8,
        style: ['defaultTopMargin', { fontSize: 12 }],
        alignment: 'right',
        stack: [
          {
            style: { bold: true },
            decoration: 'underline',
            text: `Payment Order Date: ${secondDateFormat(props.quotation?.poDate)}`,
          },
        ]
      },
      {
        marginTop: 8,
        fontSize: 13,
        fontWeight: '500',
        text: 'Bill To: '
      },
      {
        marginLeft: 12,
        marginTop: 8,
        bold: true,
        text: 'Customer: ' + props.institution.institutionName
      },
      {
        marginLeft: 12,
        marginTop: 4,
        bold: true,
        text: 'Address: ' + props.institution.address3 + ', ' + props.institution.country
      },
      {
        marginTop: 8,
        style: { fontSize: 14, bold: true },
        text: 'Dear Sir/Madam'
      },
      {
        marginTop: 8,
        text: [
          'Please effect a Renewal/',
          { text: 'New', bold: true, decoration: 'underline' },
          '/Additional premium payment for your insurance coverage as illustrated here under:'
        ]
      },
      {
        marginTop: 8,
        table: {
          widths: [20, 'auto', 'auto', '*', 'auto'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Types of Policy', style: 'tableHeader' }, { text: 'Period', style: 'tableHeader' }, { text: 'Quotation Number' }, { alignment: 'right', text: 'Premium (ETB)', style: 'tableHeader' }],
            [1, 'Health Insurance', 'TBA', props.quotation.quotationCode, { alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0)) }],
            [{
              colSpan: 4, text: [
                `Reason: New/Initial Premium.\n
                  Reason for: Additional/
                `,
                {
                  bold: true,
                  text: '/New/'
                },
                'Renewal'
              ]
            }, {}, {}, {}, {}],
            [{
              colSpan: 4, fontSize: 11.5, text: `Bank Account Information:
              Bank Name: Family Bank S.C
              Account Name/Holder: Family Insurance S.C.
              Bank Branch Name: Wuhalimat Branch
              Tell: +251-11-618-9021 Addis Ababa
              Account Number: **************
              Swift Code:  AWETAA`}, {}, {}, {}, {}],
            [{ colSpan: 4, alignment: 'right', text: 'Revenue Stamp' }, {}, {}, {}, { style: { bold: true }, alignment: 'right', text: formatCurrency(5) }],
            [{ colSpan: 4, alignment: 'right', text: 'Grand Total' }, {}, {}, {}, { style: { bold: true, fontSize: 14 }, alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0) + 5) }],
          ]
        }
      },
      {
        marginTop: 8,
        text: [
          'Therefore, you are hereby requested to settle a total premium of ',
          ` Birr ${formatNumber(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)}`,
          {
            bold: true,
            text: ` (${toWords(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)})`
          },
          ' to the above-stated bank account at the shortest possible time.'
        ]
      },
    ],
    background: [
      {
        image: bg,
        width: 600
      }
    ],
    pageMargins: [30, 140, 30, 120],
    styles: {
      defaultTopMargin: {
        margin: [0, 0, 0, 0]
      },
      tableHeader: {
        bold: true
      }
    }
  }

  pdfMake.createPdf(docDefinition).getBlob(blob => {
    file.value = URL.createObjectURL(blob)
  })
}

getPdf()
</script>
<template>
  <embed type='application/pdf' :src='file' frameborder="0" width="100%" height="800" />
</template>