<script setup>
import { useRouter } from 'vue-router';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import api from '@/scripts/api';
import { mdiHospitalBuilding, mdiPlus } from '@mdi/js';
import { ref, onMounted } from 'vue';
import customInput from '@/components/forms/custom/input.vue';
import { Field } from 'vee-validate';
import { useToast } from 'vue-toastification';
import ConfirmModal from '@/components/ConfirmModal.vue';

const providerUuid = ref('');
const contractName = ref('');
const contractNameVal = ref(null);
const contractCodeVal = ref(null);
const beginDateVal = ref(null);
const endDateVal = ref(null);
const contractCode = ref('');
const myDescription = ref('');
const endDate = ref('');
const beginDate = ref('');
const status = ref('');
const providers = ref([]);
const isLoading = ref(false);
const approveModal = ref(false);

const toast = useToast();
const router = useRouter();

const fetchProviders = async () => {
    isLoading.value = true;
    try {
        await api.get('/provider/list/name').then((data) => {
            isLoading.value = false;
            providers.value = data;
        });
    } catch (error) {
        isLoading.value = false;
        toast.error(error);
    }
};

const createContract = () => {
    contractNameVal.value.validateInput();
    contractCodeVal.value.validateInput();
    beginDateVal.value.validateInput();
    endDateVal.value.validateInput();
    if (!contractNameVal.value.hasError && !contractCodeVal.value.hasError && !beginDateVal.value.hasError && !endDateVal.value.hasError) {
        approveModal.value = !approveModal.value
    }
}

const confirmApprove = async () => {
    isLoading.value = true;
    try {
        await api.post('/payer-provider-contract', {
            providerUuid: providerUuid.value,
            contractName: contractName.value,
            contractCode: contractCode.value,
            description: myDescription.value,
            status: status.value,
            beginDate: beginDate.value,
            endDate: endDate.value,
        }).then((data) => {
            isLoading.value = false;
            toast.success(data.message);
            router.push('/ProviderContracts/Active-Contracts');
        });
    } catch (e) {
        isLoading.value = false;
        toast.error(e.message);
    }
};

onMounted(() => {
    fetchProviders();
});
</script>

<template>
    <div class="w-full h-max">
        <ConfirmModal v-model="approveModal" @confirm="confirmApprove" icon="simple-line-icons:check"
            title="Create Provider Contract" description="Are you sure you want to create this contract?"
            confirmButton="create" iconClass="text-primary p-1 text-3xl" iconWrapperClass="bg-primary rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />
        <SectionMain>
            <SectionTitleLineWithButton :icon="mdiHospitalBuilding" title="" main>
            </SectionTitleLineWithButton>

            <div class="w-full mx-auto bg-white rounded-xl shadow-md overflow-hidden p-8 m-3">
                <div class="">
                    <div class="flex flex-wrap">
                        <div class="flex flex-col space-y-2 m-2">
                            <customInput type="text" placeholder="Contract Name" ref="contractNameVal"
                                label="Contract Name" v-model="contractName" :required="true" />
                        </div>

                        <div class="flex flex-col space-y-2 m-2 flex-grow">
                            <customInput type="text" placeholder="Contract Code" ref="contractCodeVal"
                                label="Contract Code" v-model="contractCode" :required="true" />
                        </div>

                    </div>
                    <div class="flex flex-wrap">
                        <div class="flex flex-col space-y-2 m-2 w-56">
                            <customInput type="date" placeholder="" ref="beginDateVal" label="Effective Date"
                                v-model="beginDate" :required="true" />
                        </div>

                        <div class="flex flex-col space-y-2 m-2 flex-grow">
                            <customInput type="date" placeholder="" ref="endDateVal" label="End Date" v-model="endDate"
                                :required="true" />
                        </div>
                    </div>

                    <div class="sm:col-span-6 text-lg font-semibold bg-gray-100 px-2">
                        Provider Info
                    </div>
                    <div class="flex flex-wrap">
                        <div class="flex flex-col space-y-2 m-2">
                            <label for="institutionUuid" class="block font-medium leading-6 text-gray-900">Provider
                                Name</label>
                            <div class="mt-1">
                                <select v-model="providerUuid" required
                                    class="block w-80 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:leading-6">
                                    <option v-for="name in providers" :key="name.providerUuid"
                                        :value="name.providerUuid">
                                        {{ name.providerName }}
                                    </option>
                                </select>
                                <!-- <ErrorMessage name="Institution" class="text-xs text-red-600" /> -->
                            </div>
                        </div>

                        <div class="flex flex-col space-y-2 m-2">
                            <label for="status" class="block font-medium leading-6 text-gray-900">Status</label>
                            <div class="mt-2">
                                <Field as="select" name="status" v-model="status"
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:leading-6">
                                    <option value="ACTIVE">Active</option>
                                    <option value="PENDING">Pending</option>
                                </Field>
                                <p class="mt-2 text-red-600" id="email-error">
                                    <!-- {{ errors.status }} -->
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-full">
                        <label for="Description"
                            class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                        <div class="mt-1">
                            <textarea rows="4" v-model="myDescription" name="description" id="description"
                                class="block w-[35%] h-20 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end my-4 gap-x-6 border-gray-900/10 px-4 sm:px-8">
                    <button @click="createContract" type="submit"
                        class="rounded-md w-80 bg-primary py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Save
                    </button>
                </div>
            </div>

        </SectionMain>
    </div>
</template>
