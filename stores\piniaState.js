import { defineStore } from 'pinia';

export const useRowStore = defineStore({
  id: 'row',
  state: () => ({
    payerInstitutionContractUuid: '',
    institutionName: '',
    contractCode: '',
    beginDate: '',
    endDate: '',
    insuredUuid: ''
    // add more variables as needed
  }),
  actions: {
    setRowDetails(details) {
      this.payerInstitutionContractUuid = details.payerInstitutionContractUuid;
      this.institutionName = details.institutionName;
      this.contractCode = details.contractCode;
      this.contractCode = details.contractCode;
      this.endDate = details.endDate;
      this.beginDate = details.beginDate;
      // set more variables as needed
    },
    setinsuredUuid(details){
      this.insuredUuid=details.insuredUuid;

    },
    setpayerInstitutionContractUuid(details){
      this.insuredUuid=details.payerInstitutionContractUuid;

    }
  }
});
