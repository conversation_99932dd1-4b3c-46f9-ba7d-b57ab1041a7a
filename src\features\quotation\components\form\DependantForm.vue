<script setup>
import FormLayout from '@/components/FormLayout.vue';
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import { Input, Select } from '@/components/form_elements';
import { Gender, GenderCap } from '@/util/enums'

const props = defineProps({
  pending: {
    type: Boolean,
    default: false
  },
  dependant: Object,
  onSubmit: {
    type: Function,
    required: true
  },
  btnText: {
    type: String,
    required: true
  }
})

function submitForm({values}) {
  props.onSubmit(values)
}
</script>

<template>
  <FormLayout v-slot="{ submit }">
    <div class="col-span-2 flex gap-2">
      <Input
        name="firstName"
        placeholder="first name"
        label="First Name"
        validation="required"
        :value="dependant?.['firstName'] || ''"
      />
      <Input
        name="fatherName"
        placeholder="father name"
        label="Father Name"
        validation="required"
        :value="dependant?.['fatherName'] || ''"
      />
      <Input
        name="grandFatherName"
        placeholder="grandFather name"
        label="GrandFather Name"
        validation="required"
        :value="dependant?.['grandFatherName'] || ''"
      />
    </div>
    <Input
      name="title"
      label="Title"
      placeholder="title"
      :value="dependant?.['title'] || ''"
    />
    <Select
      name="relationship"
      placeholder="relationship"
      label="Relationship"
      validation="required"
      :options="['Spouse', 'Son', 'Doughter']"
      :value="dependant?.['relationship'] || 'spouse'"
    />
    <Input
      type="date"
      name="birthDate"
      label="DOB"
      validation="required"
      :value="dependant?.['birthDate'] || ''"
    />
    <Input
      name="phone"
      placeholder="phone"
      label="Phone"
      validation="required|phone"
      :value="dependant?.['phone'] || ''"
    />
    <Select
      name="gender"
      placeholder="gender"
      label="Gender"
      validation="required"
      :options="Object.values(GenderCap)"
      :value="dependant?.['gender'] || ''"
    />
    <slot name="submitBtn" :submit="submit">
      <FormSubmitButton
        :pending="pending"
        class="col-span-2"
        @click.prevent="submit(submitForm)"
      />
    </slot>
  </FormLayout>
</template>