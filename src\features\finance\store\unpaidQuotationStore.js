import { defineStore } from "pinia";
import { ref } from "vue";

export const unpaidQuotaionStore = defineStore("unpaidQuotaionStore", () => {
  const institutions = ref([])
  const done = ref(false)

  function setDone(val) {
    done.value = val
  }

  function reset() {
    institutions.value = []
    done.value = false
  }

  function add(institution) {
    institutions.value.unshift(institution)
  }
  
  function set(instu) {
    console.log(institutions)
    institutions.value = instu
  }

  function update(id, institution) {
    console.log(id, institution)
    const idx = institutions.value.findIndex(el => el.institutionUuid == id)

    if(idx == -1) return 

    institutions.value.splice(idx, 1, institution)
  }

  function remove(id) {
    institutions.value = institutions.value.filter(el => el.institutionUuid != id)
  }

  function getAll() {
    return institutions.value
  }

  return { set, getAll, institutions, remove, done, setDone, reset, add, update }
})