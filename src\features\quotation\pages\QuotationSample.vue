<script setup>
import Loader from '@/components/loader/loader.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import QuotationFormDataProvider from '@/features/quotation/components/QuotationFormDataProvider.vue'
import SampleQuotation from '@/features/quotation/components/SampleQuotation.pdf.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
</script>
<template>
  <SectionMain>
    <QuotationFormDataProvider v-slot="{ institution, packages, quotation, initPending }">
      <SampleQuotation
      v-if="!initPending"
      :institution="institution"
      :packages="packages"
      :quotation="quotation"
      ></SampleQuotation>
      <div v-else  class="h-[20rem] grid place-items-center">
        <Loader />
      </div>
    </QuotationFormDataProvider>
  </SectionMain>
</template>