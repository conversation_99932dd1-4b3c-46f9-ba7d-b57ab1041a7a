<script setup>
import * as session from "@/scripts/session";
import { useRouter, useRoute } from "vue-router";
import SectionMain from "@/components/section/SectionMain.vue";
import SectionTitleLineWithButton from "@/components/section/SectionTitleLineWithButton.vue";
import { mdiFileSign } from "@mdi/js";
import { onMounted, ref, watch } from "vue";
import api from "@/scripts/api";
import { useToast } from "vue-toastification";
import Loader from "@/components/loader/loader.vue";
import Modal from "@/components/modal.vue";
import { formatCurrency } from "@/util/utils";
import AddService from "@/components/cashClaims/addServicesModal.vue";
import EditService from "@/components/cashClaims/servicesDetailModal.vue";
import ConfirmModal from "@/components/ConfirmModal.vue";
import { debounce } from "lodash";
import customSelect from "@/components/forms/custom/select.vue";

const router = useRouter();
const route = useRoute();
const toast = useToast();
const user = session.getUser();

const institutions = ref([]);
const instUuid = ref("");
const fetchedServices = ref([]);
const insuredPersons = ref([]);
const institutionUuid = ref("");
const fullName = ref("");
const hospital = ref("");
const isLoading = ref(false);
const openServiceModal = ref(false);
const openRemark = ref(false);
const approveModal = ref(false);
const insuredUuid = ref("");
const dependentUuid = ref("");
const isListThere = ref(false);
const selectAll = ref(false);
const openServiceEditModal = ref(false);
const selected = ref([]);
const description = ref("");
const cashCreditUuid = ref("");

watch(institutionUuid, (newUuid, oldUuid) => {
  if (newUuid != oldUuid) {
    instUuid.value = newUuid;
    getCashClaimDetail(newUuid);
    fullName.value = null;
  }
});

const addService = () => {
  openServiceModal.value = !openServiceModal.value;
};

const fetchInstitutions = async () => {
  isLoading.value = true;
  await api
    .get("/institution/policy-holders/list?page=1&limit=25&status=ACTIVE")
    .then((data) => {
      institutions.value = data;
      isLoading.value = false;
    });
};

const searchInsured = async () => {
  isLoading.value = true;
  await api
    .get(
      `/insuredperson/active/search/${institutionUuid.value}?search=${fullName.value}&page=1&limit=25`
    )
    .then((data) => {
      insuredPersons.value = data;
      isListThere.value = true;
      isLoading.value = false;
    });
};

const getCashClaimDetail = async (Uuid) => {
  isLoading.value = true;
  await api
    .get(`/cash-credit/saved/lists?institutionUuid=${Uuid}&page=1&limit=25`)
    .then((data) => {
      isLoading.value = false;
      fetchedServices.value = data;
    });
};

const select = () => {
  selected.value = [];
  if (!selectAll.value) {
    fetchedServices.value.forEach((claim) => {
      selected.value.push(claim);
    });
  }
};

const selectMain = (type, name) => {
  if (type === "main") {
    fullName.value = name.insuredFullName;
    insuredUuid.value = name.insuredUuid;
    isListThere.value = false;
  } else {
    fullName.value = name.dependantFullName;
    dependentUuid.value = name.dependantUuid;
    isListThere.value = false;
  }
};

const processClaim = async (service) => {
  openRemark.value = true;
  cashCreditUuid.value = service.cashCreditUuid;
};

const addPatientDetail = async (data) => {
  isLoading.value = true;
  try {
    await api
      .post(`/cash-credit/create/with-services`, {
        cashCredit: {
          insuredPersonUuid: insuredUuid.value,
          dependantUuid: dependentUuid.value,
          institutionUuid: institutionUuid.value,
          hospital: hospital.value,
        },
        cashServices: data,
      })
      .then((data1) => {
        isLoading.value = !isLoading.value;
        toast.success("Successfully Registered");
      });
  } catch (error) {
    isLoading.value = false;
    toast.error(error.message);
  }
};

const addRemark = () => {
  approveModal.value = true;
  selected.value.forEach((service) => {
    console.log(service.cashCreditUuid);
  });
};

const confirmApprove = async () => {
  try {
    isLoading.value = !isLoading.value;
    await api
      .put(`/cash-credit/Request/credit-request`, {
        comment: description.value,
        cashCreditUuidRequest: selected.value.map((service) => {
          return { cashCreditUuid: service.cashCreditUuid };
        }),
      })
      .then((data) => {
        isLoading.value = true;
        toast.success(data.message);
      });
  } catch (error) {
    isLoading.value = false;
    toast.error(error.message);
  }
};

const closeModal = () => {
  openServiceModal.value = false;
  getCashClaimDetail(instUuid.value);
};

const closeEditModal = () => {
  openServiceEditModal.value = false;
};

const openEditModal = (id) => {
  openServiceEditModal.value = true;
  cashCreditUuid.value = id;
};

const debouncedSearch = debounce(searchInsured, 2000);

onMounted(() => {
  fetchInstitutions();
});
</script>
<template>
  <div class="w-full h-max">
    <ConfirmModal
      v-model="approveModal"
      @confirm="confirmApprove"
      icon="simple-line-icons:check"
      :title="'Process Claim'"
      :description="'Are you sure you want to Process this claim?'"
      confirmButton="Process"
      iconClass="text-primary p-1 text-3xl"
      iconWrapperClass="bg-primary rounded-full p-1"
      confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300"
    />
    <Modal
      :open="openServiceModal"
      @close="openServiceModal = !openServiceModal"
      title="Add Service Details"
    >
      <AddService
        :id="insuredUuid"
        @service-data="addPatientDetail"
        @close="closeModal"
      ></AddService>
    </Modal>
    <Modal
      :open="openServiceEditModal"
      @close="openServiceEditModal = false"
      title="Service Details"
    >
      <EditService :Uuid="cashCreditUuid" @close="closeEditModal"></EditService>
    </Modal>
    <Modal
      :open="openRemark"
      @close="openRemark = false"
      title="Add Remark"
      :autoClose="true"
    >
      <div class="flex flex-col w-80 pb-5 mt-8">
        <textarea
          rows="4"
          v-model="description"
          name="description"
          id="description"
          placeholder="Remark ..."
          class="block w-full h-20 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        ></textarea>
        <div class="flex justify-end mt-5">
          <button
            class="text-lg px-4 py-2 font-semibold text-white bg-primary rounded-lg hover:shadow-lg duration-200 hover:bg-primary"
            @click="addRemark"
          >
            Add
          </button>
        </div>
      </div>
    </Modal>
    <SectionMain>
      <SectionTitleLineWithButton :icon="mdiFileSign" :title="'Cash Claims'">
        <div class="sm:col-span-2 m-1">
          <customSelect
            label="Institution Name"
            v-model="institutionUuid"
            :items="institutions"
            placeholder="Select Institution"
            value-key="institutionUuid"
            display-key="institutionName"
          />
        </div>
      </SectionTitleLineWithButton>
      <div class="flex w-full">
        <div
          v-if="isLoading"
          class="min-w-full flex items-center mx-auto justify-center"
        >
          <Loader />
        </div>
        <div v-else class="w-full flex">
          <div class="w-full flex flex-col gap-4">
            <div class="w-full flex flex-col space-y-2">
              <div
                class="relative w-full bg-white px-4 flex-col gap-2 pt-2 sm:h-[800px]"
              >
                <div class="text-gray-500 text-lg">Add Cash Services</div>

                <div class="flex justify-between">
                  <div class="flex space-x-4 p-4">
                    <div>
                      <div class="flex flex-col">
                        <div class="mt-1.5 px-2 flex flex-col">
                          <div
                            class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600"
                          >
                            <input
                              type="text"
                              name="full Name"
                              id="fullName"
                              v-model="fullName"
                              placeholder="Search Members"
                              @input="debouncedSearch"
                              :disabled="!institutionUuid"
                              class="pl-2 w-full block border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                            />
                          </div>
                        </div>
                      </div>
                      <div
                        class="absolute z-10 mt-2 p-10 w-full bg-white rounded-md shadow-lg"
                        v-if="insuredPersons.length && isListThere"
                      >
                        <ul
                          class="max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
                          v-if="insuredPersons && insuredPersons.length != 0"
                        >
                          <li
                            v-for="(result, index) in insuredPersons"
                            :key="index"
                            class="hover:bg-gray-200 cursor-pointer p-2"
                            @click="selectMain('main', result)"
                          >
                            {{ index + 1 }}. {{ result.insuredFullName }}
                            <ul
                              class="max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
                            >
                              <li
                                v-for="(dependent, Index) in result.dependants"
                                :key="Index"
                                class="hover:bg-indigo-600 hover:text-white cursor-pointer p-2"
                                @click.stop="
                                  selectMain('Dependant', dependent, result)
                                "
                              >
                                {{ index + 1 }}.{{ Index + 1 }}.
                                {{ dependent.dependantFullName }}(
                                {{ dependent.relationship }} )
                              </li>
                            </ul>
                          </li>
                        </ul>
                        <span
                          v-else-if="
                            insuredPersons && insuredPersons.length == 0
                          "
                          >No Member Found</span
                        >
                      </div>
                    </div>
                    <div class="flex flex-col">
                      <div class="mt-1.5 flex flex-col sm:w-[220px]">
                        <div
                          class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600"
                        >
                          <input
                            type="text"
                            name="hospital"
                            id="hospital"
                            v-model="hospital"
                            placeholder="Hospital Name"
                            :disabled="!institutionUuid"
                            class="pl-2 w-full block border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                          />
                        </div>
                      </div>
                    </div>

                    <div class="flex space-x-2 mt-2">
                      <div
                        @click="addService"
                        class="bg-primary round-xl h-max flex gap-2 cursor-pointer justify-center item-center text-white px-2 py-1.5 rounded-lg"
                      >
                        <div class="font-medium text-base">
                          Add Service Details
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="flex justify-end"
                    v-if="selected && selected.length != 0"
                  >
                    <div
                      @click="processClaim"
                      class="bg-primary round-xl h-max cursor-pointer item-center text-white px-5 py-3 rounded-lg"
                    >
                      <div class="font-medium text-base">Process Claim</div>
                    </div>
                  </div>
                </div>

                <div class="max-h-full overflow-y-scroll">
                  <div class="">
                    <div class="inline-block min-w-full py-2 align-middle">
                      <table class="min-w-full divide-y divide-gray-300">
                        <thead class="border-t-2 border-solid border-black">
                          <tr>
                            <th
                              scope="col"
                              class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                            >
                              #
                            </th>

                            <th
                              scope="col"
                              class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                            >
                              Patient Full Name
                            </th>

                            <th
                              scope="col"
                              class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                            >
                              Hospital Name
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900"
                            >
                              Total Amount
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 border-b-2 border-solid text-left text-base font-semibold text-gray-900 cursor-pointer"
                            ></th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                v-model="selectAll"
                                @click="select"
                              />
                              <i class="form-icon"></i>
                            </th>
                          </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                          <tr
                            v-for="(claim, index) in fetchedServices"
                            :key="index"
                          >
                            <td
                              class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              {{ index + 1 }}
                            </td>
                            <td
                              class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              {{ claim?.fullName }}
                            </td>
                            <td
                              class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              {{ claim?.hospital }}
                            </td>
                            <td
                              class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              {{ formatCurrency(claim?.totalAmount) }}
                            </td>

                            <td
                              class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              <div class="flex">
                                <div
                                  class="bg-primary h-8 p-1 m-1 text-white text-center cursor-pointer rounded-md"
                                  @click="openEditModal(claim?.cashCreditUuid)"
                                >
                                  <div class="text-base capitalize">
                                    Details
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td
                              class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                            >
                              <input
                                type="checkbox"
                                :value="claim"
                                v-model="selected"
                              />
                              <i class="form-icon"></i>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- <table class="min-w-full min-h-full border-separate">
                                                <thead class="relative top-0 border-t-2 border-solid border-black">
                                                    <tr class="divide-gray-200 bg-gray-200">
                                                        <th scope="col">
                                                            #
                                                        </th>
                                                        <th scope="col"
                                                            class="py-2 pl-4 pr-4 px-6 text-left text-xs font-normal text-gray-900 sm:pl-0">
                                                            <span class="ml-3">Patient FullName </span>
                                                        </th>
                                                        <th scope="col"
                                                            class="px-4 py-2 text-left text-xs font-normal text-gray-900">
                                                            Hospital
                                                        </th>
                                                        <th scope="col"
                                                            class="px-4 py-2 text-left text-xs font-normal text-gray-900">
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody class="divide-y divide-gray-200 bg-section-bg">
                                                    <tr v-for="(person, index) in fetchedServices"
                                                        :key="person.cashCreditUuid">
                                                        <td class='whitespace-wrap text-sm font-base text'>
                                                            {{ index + 1 }}</td>
                                                        <td
                                                            class='whitespace-nowrap py-2 pl-1 pr-1 text-sm font-base text sm:pl-2 lg:pl-3'>
                                                            {{ person.fullName }}</td>
                                                        <td
                                                            class='whitespace-nowrap py-2 pl-1 pr-1 text-sm font-base text sm:pl-2 lg:pl-3'>
                                                            {{ person?.hospital }}</td>
                                                        <td
                                                            class='whitespace-wrap py-2 pl-1 pr-1 text-sm font-base text sm:pl-2 lg:pl-3'>
                                                            <div
                                                                class="flex justify-end bg-primary px-3 py-2 w-[40%] text-white text-center cursor-pointer rounded-md">
                                                                Service Details
                                                            </div>
                                                        </td>
                                                        <td class="flex gap-2 p-2">
                                                            <div class="text-primary"
                                                                @click="DeleteService(person.serviceProvidedUuid)">
                                                                <span class="cursor-pointer">remove</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr class="bg-white">
                                                        <td>
                                                            <div class="flex flex-col">
                                                                <div class="mt-1.5 flex flex-col">
                                                                    <div
                                                                        class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600">
                                                                        <input type="text" name="full Name" id="fullName"
                                                                            v-model="fullName" @input="debouncedSearch"
                                                                            class="pl-1 w-full block border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="absolute z-10 mt-2 p-10 w-full bg-white rounded-md shadow-lg"
                                                                v-if="insuredPersons.length && isListThere">
                                                                <ul class="max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
                                                                    v-if="insuredPersons && insuredPersons.length != 0">
                                                                    <li v-for="(result, index) in insuredPersons"
                                                                        :key="index"
                                                                        class="hover:bg-gray-200 cursor-pointer p-2"
                                                                        @click="selectMain('main', result)">
                                                                        {{ index + 1 }}. {{ result.insuredFullName }}
                                                                        <ul
                                                                            class="max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                                                                            <li v-for="(dependent, Index) in result.dependants"
                                                                                :key="Index"
                                                                                class="hover:bg-indigo-600 hover:text-white cursor-pointer p-2"
                                                                                @click.stop="selectMain('Dependant', dependent)">
                                                                                {{ index + 1 }}.{{ Index + 1 }}. {{
                                                                                    dependent.dependantFullName }}( {{
        dependent.relationship }} )
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                                <span
                                                                    v-else-if="insuredPersons && insuredPersons.length == 0">No
                                                                    Member Found</span>
                                                            </div>
                                                        </td>
                                                         <td>
                                                            <div class="flex flex-col">
                                                                <div class="mt-1.5 flex flex-col">
                                                                    <div
                                                                        class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[250px]">
                                                                        <select name="serviceGroup" id="serviceGroup"
                                                                            class="truncate px-4 w-full border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                                                            placeholder="serviceGroup"
                                                                            v-model="packageUuid">
                                                                            <option v-for="item in servicesCoverageType"
                                                                                :value="item.packageUuid">
                                                                                {{ item.packageName }}</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td> -->
                      <!-- <td>
                                                <div class="flex flex-col">
                                                    <div class="mt-1.5 flex flex-col sm:w-[220px]">
                                                        <div
                                                            class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600">
                                                            <input type="text" name="hospital" id="hospital"
                                                                v-model="hospital"
                                                                class=" pl-1 w-full block border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="flex space-x-2">
                                                    <div @click="addService"
                                                        class="bg-primary round-xl h-max flex gap-2 cursor-pointer justify-center item-center text-white px-2 py-1.5 rounded-lg">
                                                        <div class="font-medium text-base">Add Service Details
                                                        </div>
                                                    </div>
                                                    <div @click="addPatientDetail"
                                                        class="bg-green-400 round-xl h-max flex gap-2 cursor-pointer justify-center item-center text-white px-2 py-1.5 rounded-lg">
                                                        <div class="font-medium text-base">Submit</div>
                                                    </div>
                                                </div>
                                            </td>
                                            </tr>
                                            </tbody>
                                            </table> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>
