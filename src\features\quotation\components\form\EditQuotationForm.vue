<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import QuotationForm from '@/features/quotation/components/form/QuotationForm.vue'
import QuotationFormDataProvider from '@/features/quotation/components/QuotationFormDataProvider.vue'
import { formatCurrency, toast } from '@/util/utils';
import { updateInstitution } from '../../api/institutionApi';
import { issueQuotation, updateQuotation, acceptQuotation } from '../../api/quotationApi'
import Loader from '@/components/loader/loader.vue';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { QuotationStatus } from '@/util/enums';
import TableWrapper from '@/components/TableWrapper.vue';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { mdiBullhorn, mdiFilePdfBox } from '@mdi/js';
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import BaseIcon from '@/components/base/BaseIcon.vue';

</script>
<template>
  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiBullhorn" title="Quotation" main>
    </SectionTitleLineWithButton>
    <QuotationFormDataProvider
      v-slot="{ premiumAdvice, payQuotaion, contract, importInsured, submitInstitution, proceedToMembers, submitQuotation, initPending, onSubmitAccept, submitIssueQuotation, id, quotationId, institution, packages, quotation, pending }">
      <QuotationForm v-if="!initPending" quotationTitle="Edit Quotation" institutionTitle="Edit Institution"
        :pending="pending" btn-text-institution="Update Institution" btn-text-quotation="Update Quotation"
        :on-submit-institution="(values) => submitInstitution(id, values)"
        :on-submit-quotation="(values) => submitQuotation(quotationId, values)"
        :on-submit-accept-quotation="(values) => onSubmitAccept(quotationId, values)" :onSubmitQuotationIssue="(values) => submitIssueQuotation(quotationId, values, (res) => {
          if (res.success) {
            $router.push('/Quotation/Issued-Quotations')
          }
        })" :packages="packages" :institution="institution" :quotation="quotation"
        :onSubmitProceedToMembers="proceedToMembers" :show-issue-btn="quotation?.status == QuotationStatus.PENDING"
        :show-quotation-btn="!quotation?.status || quotation?.status == QuotationStatus.PENDING"
        :showAcceptBtn="quotation?.status == QuotationStatus.ISSUED"
        :show-proceed-btn="!contract && quotation?.status == QuotationStatus.ACCEPTED"
        :actions="QuotationStatus.PENDING == quotation?.status"></QuotationForm>
      <div v-else class="h-[20rem] grid place-items-center">
        <Loader />
      </div>
      <div v-if="contract" class="mt-4 flex flex-col gap-2">
        <p class="font-semibold  text-lg">Membership Category</p>
        <TableWrapper :defaultAction="false" :headers="{
          head: ['Name', 'contract Code', 'benefit', 'premium', 'modify'],
          row: ['contractName', 'contractCode', 'bene', 'prem']
        }" :rows='contract.reduce((state, el) => {
  el.bene = formatCurrency(el.benefit)
  el.prem = formatCurrency(el.premium)
  state.push(el)
  return state
}, [])'>
          <template #actions="{ row }">
            <DropdownBtn>
              <div class="cursor-pointer">
                <label class="cursor-pointer">
                  Import members
                  <input @change="(ev) => {
                    importInsured({
                      payerInstitutionContractUuid: row.payerInstitutionContractUuid,
                      institutionUuid: row.institutionUuid,
                    }, ev.target.files?.[0])
                  }" class="hidden" type="file" />
                </label>
              </div>
            </DropdownBtn>
            <DropdownBtn @click="$router.push(`/insured_and_dependants/${row.payerInstitutionContractUuid}`)">
              View members
            </DropdownBtn>
          </template>
          <template #additionalBtns="{ row }">
            <button
              @click="$router.push(`/premium_payment_request/${institution.institutionUuid}/${quotation.quotationUuid}`)"
              class="w-12 h-10 flex items-center justify-center rounded-md border border-gray-300">
              <BaseIcon :path="mdiFilePdfBox" :size="25" />
            </button>
          </template>
        </TableWrapper>
        <div class="flex gap-4"
          v-if="institution.institutionUuid && quotation.quotationUuid && contract?.[0]?.payerInstitutionContractUuid">
          <!-- <FormSubmitButton
            @click="$router.push(`/policy_debit_note/${institution.institutionUuid}/${quotation.quotationUuid}/${contract?.[0]?.payerInstitutionContractUuid}`)"
            btn-text="Policy Debit Note"
          /> -->
          <!-- Issue Policy Debit Note -->
          <FormSubmitButton :pending="pending" v-if="quotation.status == QuotationStatus.ACCEPTED"
            @click.prevent="premiumAdvice(quotationId)" class="bg-lime-500" @click="" btn-text="Issue Premium Advice" />
        </div>
      </div>
    </QuotationFormDataProvider>
  </SectionMain>
</template>