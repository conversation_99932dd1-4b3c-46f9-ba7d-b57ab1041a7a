<script setup>
import { ref } from 'vue';

import BaseIcon from '@/components/base/BaseIcon.vue';
import { mdiCheck } from '@mdi/js';
import {
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue';
// import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid';

const emit = defineEmits(['providerSelected']);

const props = defineProps({
  providers: {
    type: Array,
    required: true,
  },
});

const selected = ref({ id: 'all', name: 'All Providers' });

const emitProviderSelected = (providerUuid) => {
  emit('providerSelected', providerUuid);
};

console.log('selected', selected, 'props', props.providers);
</script>

<template>
  <Listbox as="div" v-model="selected">
    <ListboxLabel class="block text-sm font-medium leading-6 text-gray-900"
      >Provider Name</ListboxLabel
    >
    <div class="relative mt-2">
      <ListboxButton
        class="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
      >
        <span class="block truncate">{{ selected && selected.name }}</span>
        <span
          class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
        >
          <!-- <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" /> -->
        </span>
      </ListboxButton>

      <transition
        leave-active-class="transition ease-in duration-100"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <ListboxOptions
          class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
        >
          <ListboxOption
            as="template"
            v-for="provider in providers"
            :key="provider.uuid"
            :value="provider"
            v-slot="{ active, selected }"
          >
            <li
              :class="[
                active ? 'bg-indigo-600 text-white' : 'text-gray-900',
                'flex flex-row-reverse cursor-default select-none py-2 pl-3 pr-9 items-center',
              ]"
              @click="emitProviderSelected(provider.uuid)"
            >
              <span
                :class="[
                  selected ? 'font-semibold' : 'font-normal',
                  'block truncate',
                ]"
                >{{ provider.name }}</span
              >

              <span
                v-if="selected"
                :class="[active ? 'text-white' : 'text-indigo-600', ' flex   ']"
              >
                <BaseIcon :path="mdiCheck" class="text-black" :size="18" />
                <!-- <svgIcon
                  type="mdi"
                  :path="mdiCheck"
                  class="text-indigo-600 w-5 h-5"
                ></svgIcon> -->
                <!-- <CheckIcon class="h-5 w-5" aria-hidden="true" /> -->
              </span>
            </li>
          </ListboxOption>
        </ListboxOptions>
      </transition>
    </div>
  </Listbox>
</template>
