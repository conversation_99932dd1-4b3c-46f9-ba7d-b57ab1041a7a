import { makeAuthenticatedRequest } from "@/scripts/api";
import { handleError } from "vue";

export default {

  async getAllUser()
    {
        try {
            const response = await makeAuthenticatedRequest({
                method: "get",
                url: "/api/claimconnect/user/all",
                data: {},
                // headers: {
                //     'Authorization': 'Bearer '+this.token,
                //     'Accept': 'application/json',
                //     'Content-Type': 'multipart/form-data'
                // },
              });
              console.log('from response', response);
              return response;

              } catch (error) {
            return handleError(error)
        }
    },

 async getUserById(id) {

        try {
        const response = await makeAuthenticatedRequest({
            method: "get",
            url: `/api/claimconnect/user/${id}`,
            data: {},
          });
          return response;
        } 
        catch (error) {
       
            return handleError(error)
        }

    },


  async deleteUser(id) {  
    
       try {
        const response = await makeAuthenticatedRequest({
            method: "delete",
            url: `/api/claimconnect/user/${id}`,
             data:{}
        })
         return true
        
       } catch (error) {
         return handleError
       }

  }

}