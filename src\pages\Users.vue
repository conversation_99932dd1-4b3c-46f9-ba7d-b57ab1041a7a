<script setup>
import * as session from '@/scripts/session';
import { useRouter } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import SectionMain from '@/components/section/SectionMain.vue';
const router = useRouter();

const logout = () => {
  session.setUserIsLoggedIn(false);
  router.push('/login');
};
</script>
<template>
  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiBadgeAccount" title="Users" main>
      <!-- <BaseButton
          :icon="mdiPlus"
          color="whiteDark"
          @click="handleModalState('add')"
          label="Add Contract"
        /> -->
    </SectionTitleLineWithButton>
    <div class="">Users</div>
  </SectionMain>
</template>
