/**
 * 🪴 Application entry point.
 */

import { createApp } from "vue";
import App from "./App.vue";
import router from "./routes/router.js";
import { createPinia } from "pinia";
import piniaPersist from "pinia-plugin-persist";
import { useUserStore } from "../stores/userStore";
import "@assets/css/index.css";
import "./index.css";
import Toast from "vue-toastification";
import "vue-toastification/dist/index.css";
import VueDatePicker from "@vuepic/vue-datepicker";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { library } from "@fortawesome/fontawesome-svg-core";
import {
  faBars,
  faCheck,
  faTrash,
  faArrowLeftLong,
  faAngleDown,
  faEllipsisH,
  faSpinner,
  faTimes,
  faTrashAlt,
  faEdit,
  faUserEdit,
  faEye,
} from "@fortawesome/free-solid-svg-icons";
import { faPenToSquare, faEyeSlash } from "@fortawesome/free-regular-svg-icons";
import modalx from "./modals";
//import '@vuepic/vue-datepicker/dist/main.css';

import VueApexCharts from "vue3-apexcharts";

//import '@/validations/rules';
import v3ImgPreview from "v3-img-preview";

const options = {
  transition: "Vue-Toastification__fade",
  maxToasts: 2,
  newestOnTop: true,
  filterBeforeCreate: (toast, toasts) => {
    if (toasts.filter((t) => t.type === toast.type).length !== 0) {
      return false;
    }
    return toast;
  },
};

const app = createApp(App);
library.add(
  faSpinner,
  faTrash,
  faEyeSlash,
  faBars,
  faCheck,
  faArrowLeftLong,
  faAngleDown,
  faEllipsisH,
  faPenToSquare,
  faTimes,
  faTrashAlt,
  faEdit,
  faUserEdit,
  faEye
);

const pinia = createPinia();
pinia.use(piniaPersist);
app.component("VueDatePicker", VueDatePicker);
app.component("icon", FontAwesomeIcon);
app.use(pinia);
app.use(router);
app.use(modalx);
app.use(Toast, options);
app.use(v3ImgPreview, {
  keyboard: false,
  escClose: true,
  showToolbar: true,
});
app.use(VueApexCharts);

router.beforeEach((from, to, next) => {
  // ✅ Check if user has logged in to decide if we should show the login page or not.
  const store = useUserStore(pinia);
  if (from.name != "login" && !store.userIsLoggedIn) {
    next({
      path: "login",
      replace: true,
    });
  } else {
    next();
  }
});

app.mount("#app");
