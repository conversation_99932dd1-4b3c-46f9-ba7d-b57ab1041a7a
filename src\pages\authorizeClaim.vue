<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import { mdiBadgeAccount, mdiChevronDoubleRight, mdiChevronDoubleLeft, mdiCurrencyUsd } from '@mdi/js';
import * as session from '@/scripts/session';
import Modal from '@/components/modal.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import loader from '@/components/loader/loader.vue';
import { computed, ref, watch, onMounted } from 'vue';
import BaseButton from '@/components/base/BaseButton.vue';
import api from '@/scripts/api';
import NoData from '@/assets/img/no-data-here.png';
import selectMenu from '@/components/selectMenu.vue';
import invoice from '@/components/invoice.vue';
import { useToast } from 'vue-toastification';
import { useRoute, useRouter } from 'vue-router';
import { formatCurrency } from '@/util/utils';
import TableWrapper from '@/components/tables/myTable/TableWrapper.vue';

const user = session.getUser();
const privileges = user?.privileges;
const router = useRouter();
const toast = useToast();
const emit = defineEmits(['delete']);

const institutionUuid = ref(null);
const cashClaims = ref(false);
const creditClaims = ref(false);
const totalPages = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(25);
const pageSizes = ref([25, 50, 75, 100, 125, 150]);
const claims = ref([]);
const selectedBatch = ref([]);
const institutions = ref([]);
const loading = ref(false);
const claimTypes = ref('');
const roleType = ref('');
const orderBy = ref('asc');
const openInvoiceModal = ref(false);
const distinctProviders = ref([]);
const selected = ref([]);
const selectAll = ref(false);
const search = ref(sessionStorage.getItem("searchTerm") || "");

watch(search, (newSearchTerm, oldSearch) => {
    sessionStorage.setItem("searchTerm", newSearchTerm);
    if (creditClaims.value == true && newSearchTerm != oldSearch) {
        fetchClaims(newSearchTerm);
    } else if (newSearchTerm != oldSearch && cashClaims.value) {
        fetchCashClaims(newSearchTerm);
    }
});

watch(institutionUuid, (newUuid, oldUuid) => {
    if (newUuid != oldUuid) {
        filterByInstitution(newUuid)
    }
})

onMounted(async () => {
    await fetchClaims('');
    creditClaims.value = true;
});

const handleInvoiceDetailModal = () => {
    openInvoiceModal.value = !openInvoiceModal.value;
    selectedBatch.value = selected.value
};

const closeInvoiceDetailModal = () => {
    openInvoiceModal.value = false
}

const fetchClaims = async (searchKey) => {
    loading.value = true;
    await api.get(`/claim/payment/requested/list?search=${searchKey}&status=Authorized&page=1&limit=25`).then((data) => {
        claims.value = data;
        claimTypes.value = 'Authorized';
        roleType.value = 'Payer';
        loading.value = false;
    });
};
const fetchCashClaims = async (searchKey) => {
    loading.value = true;
    await api.get(`/cash-credit/requested/lists?searchKey=${searchKey}&status=Authorized&page=1&limit=25`).then((data) => {
        claims.value = data;
        claimTypes.value = 'Authorized';
        roleType.value = 'Payer';
        loading.value = false;
    });
};

const select = () => {
    selected.value = [];
    if (!selectAll.value) {
        claims.value.forEach((claim) => {
            selected.value.push(claim)
        })
    }
};

const handleSort = (column) => {
    if (orderBy.value === 'asc') {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return a[column].localeCompare(b[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'desc';
    } else {
        claims.value.sort((a, b) => {
            if (a[column] && b[column]) {
                return b[column].localeCompare(a[column]);
            } else {
                return 0;
            }
        });
        orderBy.value = 'asc';
    }
};

watch(claims, () => {
    distinctProviders.value = [
        ...new Set(claims.value.map((claim) => claim.providerName)),
    ].map((providerName, index) => {
        const providerUuid = claims.value.find(
            (claim) => claim.providerName === providerName
        ).providerUuid;
        return {
            id: index + 1,
            name: providerName,
            uuid: providerUuid,
        };
    });
});

const filterByProvider = (providerUuid) => {
    if (providerUuid == 'all' || !providerUuid) {
        return claims.value;
    } else {
        claims.value = claims.value.filter((claim) => {
            return claim.providerUuid === providerUuid;
        });
    }
};

const filterByInstitution = (institutionUuid) => {
    if (institutionUuid == null) {
        return claims.value;
    } else {
        return claims.value = claims.value.filter((claim) => {
            console.log(claim.institutionUuid === institutionUuid);
            claim.institutionUuid === institutionUuid;
        });
    }
};

const fetchInstitutions = async () => {
    try {
        loading.value = true;
        await api.get(`/institution/policy-holders/list?page=1&limit=25&status=ACTIVE`).then((data) => {
            institutions.value = data;
            loading.value = false;
        });
    } catch (error) {
        loading.value = false;
        toast.error(error.message);
    }
}

const creditTab = () => {
    creditClaims.value = true;
    cashClaims.value = false;
    fetchClaims('');
}

const cashTab = () => {
    creditClaims.value = false;
    cashClaims.value = true;
    fetchCashClaims('');
    fetchInstitutions();
}
</script>
<template>
    <div class="w-full h-max">
        <SectionMain>
            <SectionTitleLineWithButton :icon="mdiBadgeAccount" :title="claimTypes + ' ' + 'Claims'" main>
                <form id="searchEliService" class="w-full md:w-[40%] lg:w-[30%] mt-2">
                    <label for="default-search"
                        class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                    <div class="relative">
                        <input type="text" v-model="search" id="default-search"
                            class="block w-full p-4 ps-8 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="Search . . ." required />
                        <button type="submit" @click=""
                            class="text-white absolute end-2.5 bottom-2.5 bg-primary0 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Search
                        </button>
                    </div>
                </form>
            </SectionTitleLineWithButton>
            <div class="flex p-2 space-x-1">
                <div class="w-max px-3 py-2 border-2 border-solid border-gray-500 bg-gray-50 sm:ml-90 rounded-lg cursor-pointer hover:text-black hover:text-medium"
                    :class="creditClaims
                        ? 'border-b-2 border-gray-900 px-2 py-1 bg-primary text-white'
                        : 'text-black'
                        " @click="creditTab()">
                    <p class="">Credit Services</p>
                </div>
                <div class="w-max px-3 py-2 border-2 border-solid border-gray-500 bg-gray-50 rounded-lg cursor-pointer hover:text-gray-900 hover:text-medium"
                    :class="cashClaims
                        ? 'border-b-2 border-gray-900 px-2 py-1 bg-primary text-white'
                        : 'text-black'
                        " @click="cashTab()">
                    <p class="">Cash Services</p>
                </div>
            </div>
            <div class="min-h-[70vh]">
                <div v-if="loading" class="flex items-center justify-center h-[40vh]">
                    <loader />
                </div>

                <div class="h-full" v-else>
                    <Modal :open="openInvoiceModal" @close="closeInvoiceDetailModal" title="Payment Information"
                        titleClass="border-b-2 border-gray-400 pb-2">
                        <invoice :claimsToBePayed=selectedBatch @payment-completed="handleInvoiceDetailModal" />
                    </Modal>

                    <div v-if="roleType === 'Payer'"
                        class="px-4 sm:px-6 lg:px-8 bg-[#EABFFF]/10 py-4 flex items-center justify-between">
                        <div class="flex items-center" v-if="creditClaims">
                            <selectMenu :providers="distinctProviders" @providerSelected="filterByProvider" />
                        </div>

                        <div class="sm:col-span-1 m-1" v-else>
                            <label for="insurance" class="text-sm font-normal">Institution Name</label>
                            <div
                                class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[200px]">
                                <select name="institutionUuid" id="institutionUuid" v-model="institutionUuid"
                                    class="truncate block w-full px-4 py-2 rounded-md bg-gray-100 border-1 focus:border-gray-500 focus:bg-white focus:ring-0">
                                    <option :value=null class="py-1 hover:bg-red-500 hover:text-white">
                                        All
                                    </option>
                                    <option v-for="provider in institutions" :key="provider.institutionUuid"
                                        :value="provider.institutionUuid"
                                        class="py-1 hover:bg-red-500 hover:text-white">
                                        {{ provider.institutionName }}
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <div>
                                <span class="text-xs text-primary mb-2">{{ selected.length }} Batch
                                    selected</span>
                            </div>
                            <BaseButton icon-w="w-4" icon-h="h-4" color="success"
                                class="bg-primary hover:bg-primary0 focus:ring-0" label="Proceed
        payment" :icon="mdiCurrencyUsd" @click="handleInvoiceDetailModal" />
                        </div>
                    </div>

                    <div class="min-h-[70vh] flex items-center justify-center flex-col gap-5"
                        v-if="claims && claims.value == 0 && !loading">
                        <img :src="NoData" alt="" />
                        <div class="text-sm text-primary font-semibold">
                            No {{ claimTypes }} claims found
                        </div>
                    </div>
                    <div class="px-4 sm:px-6 lg:px-8" v-if="claims && claims.length">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                        <tr>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                                No.
                                            </th>

                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                                Policy Holder Name
                                            </th>

                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900"
                                                v-if="creditClaims">
                                                Provider Name
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                                Batch Code
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                                Total Amount
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900"
                                                @click="handleSort('claimDate')">
                                                Requested Date
                                                <span v-if="orderBy === 'asc'">▲</span>
                                                <span v-if="orderBy === 'desc'">▼</span>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                                Status
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer">
                                                <input type="checkbox" v-model="selectAll" @click="select" />
                                                <i class="form-icon"></i>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                        <!-- clickable cell -->
                                        <tr v-for="(claim, index) in claims" :key="claim.claimUuid" :class="[
                                            ' cursor-pointer hover:bg-gray-100',
                                            selected.includes(claim.claimUuid)
                                                ? ' bg-primary0 hover:bg-primary0'
                                                : 'text-gray-900 ',
                                        ]">
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ index + 1 }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.institutionName }}({{ claim?.institutionPhone }})
                                            </td>
                                            <td class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ claim?.providerName }}({{ claim?.providerPhone }})
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.batchCode }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ formatCurrency(claim?.totalAmount) }}
                                            </td>
                                            <td
                                                class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                {{ claim?.authorizedDate }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500"
                                                v-if="creditClaims">
                                                {{ claim?.claimStatus }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500"
                                                v-else>
                                                {{ claim?.status }}
                                            </td>
                                            <td
                                                class="whitespace-wrap px-3 py-2 border-gray-600 text-base text-gray-500">
                                                <input type="checkbox" :value="claim" v-model="selected" />
                                                <i class="form-icon"></i>
                                            </td>
                                            <td
                                                class="whitespace-wrap flex px-3 py-2 border-gray-600 text-base text-gray-500">
                                                <div class="bg-primary px-2 py-1  text-white text-center cursor-pointer rounded-md"
                                                    @click="router.push({
                                                        name: 'claim-batch-details',
                                                        params: {
                                                            id: claim?.batchCode,
                                                            Uuid: claim?.providerUuid ? claim?.providerUuid : 'cash',
                                                            status: claim?.claimStatus ? claim?.claimStatus : claim?.status
                                                        },
                                                    })">
                                                    <div class="text-base capitalize">Details</div>
                                                </div>

                                                <div @click="router.push({
                                                    name: 'CPO',
                                                    params: {
                                                        id: claim?.batchCode,
                                                        Uuid: claim?.providerUuid,
                                                        status: claim?.claimStatus
                                                    },
                                                })"
                                                    class="bg-secondary px-2 py-1 ml-2  text-white text-center cursor-pointer rounded-md">
                                                    <div class="text-base capitalize">View CPO</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="w-full flex justify-end mt-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="bg-white">
                                            <BaseButton :icon="mdiChevronDoubleLeft" label="Previous"
                                                title="previous page" color="whiteDark" @click="previousPage"
                                                :disabled="currentPage === 1" />
                                        </div>
                                        <div>
                                            <div>
                                                Items per page:
                                                <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                                                    <option v-for="size in pageSizes" :key="size" :value="size">
                                                        {{ size }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div>
                                            <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page"
                                                color="whiteDark" @click="nextPage"
                                                :disabled="currentPage === totalPages" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SectionMain>
    </div>
</template>
