<script setup>
import pdfMake from 'pdfmake/build/pdfmake'
import pdfFonts from 'pdfmake/build/vfs_fonts'
import { getBgbase64 } from '@/util/utils'
import { computed, ref } from 'vue';
import { formatCurrency, formatNumber, secondDateFormat } from '@/util/utils'
import { ToWords } from 'to-words'

const toWords = new ToWords({
  localeCode: 'en-IN',
})

// pdfMake.vfs = pdfFonts.pdfMake.vfs

const props = defineProps({
  institution: {
    type: Object,
    required: true,
  },
  quotation: {
    type: Object,
    required: true,
  },
  members: {
    type: Object,
    required: true,
  },
  contract: {
    type: Object,
    required: true,
  },
  packages: {
    type: Object,
    required: true,
  }
})

const quotation = props.quotation.quoatedServices.map(el => {
  return {
    ...el,
    packageName: props.packages.find(pack => pack.packageUuid == el.packageUuid)?.packageName
  }
})

const group = Object.groupBy(quotation, ({ packageName }) => packageName)

console.log(group)

const premium = computed(() => {
  return props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0)
})

function getNumberOfInsured() {
  const pack = Object.groupBy(props.quotation.quoatedServices, ({ packageUuid }) => packageUuid)
  const res = pack[Object.keys(pack)?.[0]]
  return res.reduce((sum, el) => {
    return sum += el.numberOfInsured
  }, 0)
}

function getNumberOfDependants() {
  const pack = Object.groupBy(props.quotation.quoatedServices, ({ packageUuid }) => packageUuid)
  const res = pack[Object.keys(pack)?.[0]]
  return res.reduce((sum, el) => {
    return sum += el.numberOfInsured * el.description
  }, 0)
}

function totalSum() {
  return getNumberOfDependants() + getNumberOfInsured()
}

function getGrandTotal(name) {
  const dependantsLength = props.members.reduce((sum, el) => sum += el.dependants.length, 0)
  return props.members.reduce((sum, member) => {
    const found = group[name]
      .find(el => el.description == member.dependants.length)
    if (!found) return sum
    return sum += found?.coverage
  }, 0)
}

function getGrandTotalPremium() {
  const dependantsLength = props.members.reduce((sum, el) => sum += el.dependants.length, 0)
  return props.members.reduce((sum, member) => {
    const premium = Object.keys(group)
      .reduce((sum, key) => {
        const res = group[key].find(el => el.description == member.dependants.length)
        return sum += res?.premium || 0
      }, 0)
    return sum += premium
  }, 0)
}

const file = ref()
async function genPdf() {
  const bg = await getBgbase64('/crtificate_header_anbessa.png')
  const docDefinition = {
    content: [
      {
        alignment: 'center',
        color: 'red',
        fontSize: 16,
        bold: true,
        text: 'Policy Debit Note'
      },
      {
        text: [
          "Debit Note Date: ",
          {
            decoration: 'underline',
            text: secondDateFormat(props.quotation?.acceptedDate)
          }
        ]
      },
      {
        marginTop: 8,
        text: [
          "Debit Note Number: ",
          {
            decoration: 'underline',
            text: props.quotation?.policyDebitNumber
          }
        ]
      },
      {
        marginTop: 20,
        text: [
          'Customer Name: ',
          {
            decoration: 'underline',
            text: props.institution.institutionName
          }
        ]
      },
      {
        marginTop: 8,
        columns: [
          {
            width: '22%',
            text: 'Customer Address: '
          },
          {
            columnGap: 5,
            columns: [
              {
                stack: [
                  {
                    text: [
                      'Sub City: ',
                      {
                        decoration: 'underline',
                        text: props.institution?.address2
                      }
                    ]
                  },
                  {
                    marginTop: 8,
                    text: [
                      'Phone Number: ',
                      {
                        decoration: 'underline',
                        text: props.institution?.telephone
                      }
                    ]
                  }
                ]
              },
              {
                text: [
                  'Woreda: ',
                  {
                    decoration: 'underline',
                    text: props.institution?.address1
                  }
                ]
              },
            ]
          }
        ]
      },
      {
        marginTop: 8,
        columns: [
          {
            text: [
              'Policy Number: ',
              {
                decoration: 'underline',
                text: props.institution?.institutionInsuranceNumber || 'New'
              }
            ]
          },
          {
            text: [
              'Quote Number: ',
              {
                decoration: 'underline',
                text: props.quotation?.quotationCode
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        columns: [
          {
            text: [
              'Class Of Business: ',
              {
                decoration: 'underline',
                text: 'Medical'
              }
            ]
          },
          {
            text: [
              'Type of Alteration: ',
              {
                decoration: 'underline',
                text: 'New Policy'
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        columns: [
          {
            text: [
              'Number of Insured Lives:	Main Member: ',
              {
                decoration: 'underline',
                text: formatNumber(getNumberOfInsured())
              }
            ]
          },
          {
            text: [
              'Dependent: ',
              {
                decoration: 'underline',
                text: formatNumber(getNumberOfDependants())
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        text: [
          'Total Sum Assured: ',
          {
            decoration: 'underline',
            text: formatNumber(totalSum())
          }
        ]
      },
      {
        marginTop: 8,
        columns: [
          {
            text: [
              'Period of Insurance: From: ',
              {
                decoration: 'underline',
                text: secondDateFormat(props.contract?.beginDate)
              }
            ]
          },
          {
            text: [
              'To: ',
              {
                decoration: 'underline',
                text: secondDateFormat(props.contract?.endDate)
              }
            ]
          }
        ]
      },
      {
        marginTop: 8,
        columnGap: 10,
        columns: [
          {
            width: '13%',
            text: 'Premium: '
          },
          {
            width: 'auto',
            text: 'First: ',
          },
          {
            width: '15%',
            canvas: [{ lineWidth: 8, type: 'rect', x: 0, y: 2, w: 10, h: 10 }]
          },
          {
            width: 'auto',
            text: 'Renewal: ',
          },
          {
            width: '15%',
            canvas: [{ type: 'rect', x: 0, y: 2, w: 10, h: 10 }]
          },
          {
            width: 'auto',
            text: 'Additional: ',
          },
          {
            width: '15%',
            canvas: [{ type: 'rect', x: 0, y: 2, w: 10, h: 10 }]
          },
        ]
      },
      {
        marginTop: 8,
        text: 'Amount: '
      },
      {
        marginTop: 8,
        columns: [
          {
            width: '15%',
            text: ''
          },
          {
            stack: [
              {
                text: [
                  'Premium:   ',
                  {
                    decoration: 'underline',
                    text: formatNumber(premium.value)
                  }
                ]
              },
              {
                marginTop: 8,
                text: [
                  'Less WHT: 2%   ',
                  {
                    decoration: 'underline',
                    text: formatNumber(premium.value * 2 / 100)
                  }
                ]
              },
              {
                marginTop: 8,
                text: 'Add Stamp Duty: Br 5.00 for new policy only'
              },
              {
                marginTop: 8,
                text: [
                  'Net Premium:   ',
                  {
                    decoration: 'underline',
                    text: formatNumber(premium.value + 5 - premium.value * 2 / 100)
                  }
                ]
              },
            ]
          },
        ]
      },
      {
        marginTop: 8,
        text: [
          'Net Amount in Words: ',
          {
            bold: true,
            decoration: 'underline',
            text: toWords.convert(premium.value + 5 - premium.value * 2 / 100, {})
          }
        ]
      },
      {
        marginTop: 55,
        bold: true,
        fontSize: 14,
        alignment: 'center',
        stack: [
          { text: 'For and on behalf of' },
          { text: 'Family INSURANCE COMPANY (S.C)' },
          {
            bold: false,
            fontSize: 12,
            text: [
              'Signed on this  ',
              {
                decoration: 'underline',
                bold: true,
                text: (new Date()).getDate()
              },
              '  day of  ',
              {
                decoration: 'underline',
                bold: true,
                text: new Date(0, (new Date()).getMonth() + 1).toLocaleString('default', { month: 'long' })
              },
              "  " + (new Date()).getFullYear()
            ]
          }
        ],
        pageBreak: 'after',
      },
      {
        pageMargins: [20, 10, 20, 10],
        style: { fontSize: 6 },
        table: {
          widths: ['auto', 'auto', 'auto', 10, 12, ...Array.from(Object.keys(group), () => 'auto'), 'auto'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Employee Name', style: 'tableHeader' }, { text: 'Relationship', style: 'tableHeader' }, { text: 'Gender', style: 'tableHeader' }, { text: 'Size', style: 'tableHeader' }, ...Object.keys(group).map(el => ({ text: el, style: 'tableHeader' })), { text: 'Total Annual Premium', style: 'tableHeader' }],
            ...props.members.reduce((members, member, idx) => {
              const row = []
              row.push(idx + 1)
              row.push(`${member.insuredTitle} ${member.firstName} ${member.fatherName} ${member.grandFatherName}`)
              row.push('Employee')
              row.push(member.gender == 'Male' ? 'M' : 'F')
              row.push(member.dependants.length)
              Object.keys(group)
                .forEach(key => {
                  const res = group[key].find(el => el.description == member.dependants.length)
                  row.push(res?.coverage > 0 ? formatNumber(res?.coverage) : 0)
                })
              row.push(formatNumber(Object.keys(group)
                .reduce((sum, key) => {
                  const res = group[key].find(el => el.description == member.dependants.length)
                  return sum += res?.premium || 0
                }, 0)))
              members.push(row)
              member.dependants.forEach((dep, index) => {
                const row = []
                row.push(`${idx + 1}.${index + 1}`)
                row.push(`${dep.dependantFirstName} ${dep.dependantFatherName} ${dep.dependantGrandFatherName}`)
                row.push(dep.relationship)
                row.push(member.dependantGender == 'Male' ? 'M' : 'F')
                row.push('')
                Object.keys(group)
                  .forEach(key => {
                    const res = group[key].find(el => el.description == member.dependants.length)
                    row.push('')
                  })
                row.push('')
                members.push(row)
              })
              return members
            }, []),
            [
              {},
              { text: 'Total...', bold: true },
              {}, {}, {},
              ...Object.keys(group)
                .map(el => ({ bold: true, text: formatNumber(getGrandTotal(el)) })),
              { bold: true, text: formatNumber(getGrandTotalPremium()) }
            ]
          ]
        }
      }
    ],
    background: [
      {
        image: bg,
        width: 600
      }
    ],
    pageMargins: [15, 140, 15, 100],
    styles: {
      defaultTopMargin: {
      },
      tableHeader: {
        bold: true
      }
    }
  }

  pdfMake.createPdf(docDefinition).getBlob(blob => {
    file.value = URL.createObjectURL(blob)
  })
}

genPdf()
console.log(props)
</script>
<template>
  <embed type='application/pdf' :src='file' frameborder="0" width="100%" height="600" />
</template>