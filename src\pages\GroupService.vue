<script setup>
import LayoutAuthenticated from "@/layouts/LayoutAuthenticated.vue";
import SectionMain from "@/components/section/SectionMain.vue";
import { makeAuthenticatedRequest } from "@/scripts/api";
import { mdiOfficeBuilding, mdiPlus, mdiAccountWrench } from "@mdi/js";
import SvgIcon from "@jamescoyle/vue-icon";
import { onMounted, ref } from "vue";
import Modal from "@/components/modal.vue";
import GroupServiceList from "@/components/GroupService/GroupServiceList.vue";
import AddGroupService from "@/components/forms/AddGroupService.vue";
import SectionTitleLineWithButton from "@/components/section/SectionTitleLineWithButton.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import loader from "@/components/loader/loader.vue";

const open = ref(false);
const deleteInstitutionModalOpen = ref(false);
const ServiceGroup = ref([]);
const loading = ref(false);
const deleteLoading = ref(false);

const AddService = () => {
  handleModalClose("Create");
  fetchServiceGroup();
};

const handleModalClose = async (modalValue) => {
  if (modalValue === "delete") {
    deleteInstitutionModalOpen.value = !deleteInstitutionModalOpen.value;
  } else {
    open.value = !open.value;
    await fetchServiceGroup();
  }
};

onMounted(async () => {
  await fetchServiceGroup();
});

const fetchServiceGroup = async () => {
  loading.value = true;
  await makeAuthenticatedRequest({
    method: "Get",
    url: "api/payer/claimconnect/service-group/list",
  }).then((data) => {
    ServiceGroup.value = data;
    loading.value = false;
  });
};
</script>
<template>
  <div class="w-full h-max">
    <Modal :open="open" @close="handleModalClose('Create')" title="Add Coverage Type">
      <AddGroupService @save="AddService('Create')"></AddGroupService>
    </Modal>
      <SectionMain>
        <SectionTitleLineWithButton :icon="mdiAccountWrench" title="Coverage Types" main>
          <BaseButton :icon="mdiPlus" title="Coverage Types" color="whiteDark" @click="open = !open"
            label="Add Coverage Types" class="mx-4 my-2" />
        </SectionTitleLineWithButton>
        <div class="min-h-[70vh]">
          <div v-if="loading" class="flex items-center justify-center h-[40vh]">
            <loader />
          </div>

          <GroupServiceList v-if="!loading" :ServiceGroup="ServiceGroup" @save="fetchServiceGroup"
            @delete="(id) => CatchInstitutionToBeDeleted(id)" />
        </div>
      </SectionMain>
  </div>
</template>
