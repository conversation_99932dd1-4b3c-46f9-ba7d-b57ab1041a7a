<script setup>
import * as session from "@/scripts/session";
import { useRouter } from "vue-router";
import { ref, computed } from "vue";
import LayoutAuthenticated from "@/layouts/LayoutAuthenticated.vue";
import SectionMain from "@/components/section/SectionMain.vue";
import SectionTitleLineWithButton from "@/components/section/SectionTitleLineWithButton.vue";
import { mdiAccountGroupOutline, mdiHospitalBuilding, mdiPlus } from "@mdi/js";
import InsuredPersonRegistrationForm from "@/components/forms/InsuredPersonRegistrationForm.vue";
import Modal from "@/components/modal.vue";
import InsuredPersonsList from "@/components/tables/insured-persons-list.vue";
import loader from "@/components/loader/loader.vue";
import AddInsuredPerson from "../components/institution/AddInsuredPerson.vue";
import { onMounted } from "vue";
import { makeAuthenticatedRequest } from "@/scripts/api";
import { mdiMagnify } from "@mdi/js";
import SvgIcon from "@jamescoyle/vue-icon";
import AddMember from "../components/forms/AddMember.vue";
import { useRowStore } from "../../stores/piniaState";
const openImportModal = ref(false);
const openAddModal = ref(false);
const importedFile = ref();
const isLoading = ref(false);

const term = ref("");
const router = useRouter();
const insuredPersons = ref([]);

const loading = ref(false);
const insured = ref([]);

const institutionName = ref("");
const payerInstitutionContractUuid = ref("");
const beginDate = ref("");
const endDate = ref("");
const userow = useRowStore();
institutionName.value = userow.institutionName;
payerInstitutionContractUuid.value = userow.payerInstitutionContractUuid;
beginDate.value = userow.beginDate;
endDate.value = userow.endDate;

onMounted(async () => {
  await fetchInsuredPersons();
});
const handleModalClose = () => {
  openAddModal.value = !openAddModal.value;
};

const filteredItems = computed(() => {
  if (!term.value) {
    return insured.value;
  } else {
    return insured.value.filter((item) =>
      item.firstName.toLowerCase().includes(term.value.toLowerCase())
    );
  }});

const logout = () => {
  session.setUserIsLoggedIn(false);
  router.push("/login");
};

const fetchInsuredPersons = async () => {
  loading.value = true;
  await makeAuthenticatedRequest({
    method: "Get",
    url: "/api/claimconnect/insuredperson/all?page=1&limit=25",
  }).then((data) => {
    insured.value = data;
    loading.value = false;
  });
};

const handleFileChange = async (event) => {
  importedFile.value = event.target.files[0];
};

const submitClaimAttachment = async () => {
  isLoading.value = true;
  let formData = new FormData();
  formData.append("files", importedFile.value);
  try {
    await makeAuthenticatedRequest({
      headers: {
        "Content-Type": "multipart/form-data",
      },
      method: "POST",
      url: `api/claimconnect/insuredperson/import?institutionUuid=bfdabcc8-d9a4-41f7-b1a3-7affc445637d&payerInstitutionContractUuid=${route.params.id}`,
      data: formData,
    }).then((data) => {
      isLoading.value = false;
      toast.success(data.message);
      console.log("file upload", data);
    });
  } catch (error) {
    isLoading.value = false;
    console.log(error);
    toast.error(error);
  }
};
</script>
<template>
  <div class="w-full h-max">
    <!-- Create Insured person for institutions form -->
    <Modal
      :open="openAddModal"
      @close="handleModalClose('Create')"
      title="Add Member"
    >
      <AddMember @save="handleModalClose"></AddMember>
    </Modal>

    <Modal
      :open="openImportModal"
      @close="openImportModal = false"
      title="Import Members"
    >
      <form
        @submit="submitClaimAttachment"
        enctype="multipart/form-data"
        class="mt-10 flex flex-col gap-y-10"
      >
        <div
          class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[383px]"
        >
          <input
            type="file"
            name="attachment"
            id="attachment"
            class="px-4 block flex-1 border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
            placeholder="attachment"
            @change="handleFileChange($event)"
          />
        </div>
        <div class="flex w-full justify-center">
          <button
            class="bg-primary/80 hover:bg-primary hover:shadow-lg duration-200 px-10 py-1 text-lg text-white font-semibold rounded-lg"
          >
            Import
          </button>
        </div>
      </form>
    </Modal>

      <SectionMain>
        <div class="flex">
          <div class="flex justify-start text-lg">
            <svg-icon type="mdi" :path="mdiAccountGroupOutline"></svg-icon>
            <div class="px-2 font-semibold">{{ institutionName }}</div>
          </div>
          <div class="ml-96 px-4 mb-8">
            <div
              class="bg-primary round-xl flex gap-0.5 cursor-pointer item-center text-white px-3 py-2 rounded-lg"
              @click="openAddModal = !openAddModal"
            >
              <div class="text-white">
                <svg-icon type="mdi" :path="mdiPlus"></svg-icon>
              </div>
              <div class="font-medium text-base">Add Members</div>
            </div>
          </div>
        </div>

        <div class="bg-white py-9">
          <div class="flex">
            <div class="flex-col">
              <div class="flex">
                <div class="px-4">Total Amount claimed</div>
                <div class="font-semibold">120,000 Br</div>
              </div>
              <div class="flex py-4">
                <div class="px-4">Group Policy :</div>
                <div class="">Contract-0001</div>
              </div>
            </div>
            <div class="flex-col">
              <div class="flex">
                <div class="px-4">Total Amount settled</div>
                <div class="font-semibold px-1">120,000 Br</div>
              </div>
              <div class="flex py-4">
                <div class="px-4">Effective Date</div>
                <div class="px-2">{{ beginDate }} to</div>
                <div class="">{{ endDate }}</div>
              </div>
            </div>
            <div class="flex-col">
              <div class="px-9 flex">
                <div class=" ">Total Members</div>
                <div class="font-semibold px-2">25</div>
              </div>
              <div class="px-6">
                <div
                  class="bg-primary round-xl flex gap-0.5 cursor-pointer item-center text-white px-3 py-2 rounded-lg"
                  @click="openImportModal = !openImportModal"
                >
                  <div class="text-white"></div>
                  <div class="font-medium text-base">Import Members</div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex px-4">
            <hr
              class="w-48 h-1 px-3 my-4 bg-red-300 border-0 rounded md:my-5"
            />
            <hr
              class="w-64 h-1 px-3 my-4 bg-yellow-300 border-0 rounded md:my-5"
            />
            <hr
              class="w-48 h-1 px-3 my-4 bg-green-300 border-0 rounded md:my-5"
            />
            <hr
              class="w-64 h-1 px-3 my-4 bg-blue-300 border-0 rounded md:my-5"
            />
          </div>
          <div class="flex-col">
            <div class="font-semibold justify-center flex">
              Top claims by category
            </div>
            <div class="flex mt-4">
              <!-- <div class="px-4 flex-col">
            <div class="flex-col text-red-300">12000.00 Br</div>
            <div class="font-semibold ">Diagnosis</div>
          </div> -->
              <div class="ml-48">
                <div class="text-yellow-300">12000.00 Br</div>
                <div class="font-semibold">Laboratory</div>
              </div>
              <div class="ml-32">
                <div class="text-green-300">12000.00 Br</div>
                <div class="font-semibold">Prescription</div>
              </div>
              <div class="ml-32">
                <div class="text-blue-300">12000.00 Br</div>
                <div class="font-semibold">Radiology</div>
              </div>
            </div>
          </div>
        </div>
        <div class="py-6 pr-14 justify-end flex">
          <div class="">
            <form class="  ">
              <div class="form-group">
                <input
                  type="text"
                  class="rounded-3xl !border !border-[#C7BEBE] focus:ring-0 ring-0"
                  v-model="term"
                  placeholder="Search"
                />
              </div>
            </form>
            <!-- rounded-3xl !border !border-[#C7BEBE] focus:ring-0 ring-0 -->
          </div>
        </div>

        <!-- Insured Persons list  -->

        <div class="min-h-[70vh]">
          <!-- <div v-if="loading" class="flex items-center justify-center h-[40vh]">
            <loader />
          </div> -->
          <AddInsuredPerson :filteredItems="filteredItems" />
        </div>
      </SectionMain>
  </div>
</template>
../stores/piniaState