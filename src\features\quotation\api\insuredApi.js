import ApiService from '@/service/ApiService'
import { getQueryFormObject } from '@/util/utils'

const path = '/insuredperson'

export async function importInsuredAndDependant(query = {}, data, config = {}) {
  const api = new ApiService()
  const qs = getQueryFormObject(query)
  return await api.addAuthenticationHeader().post(`${path}/import-insured-and-dependant${qs}`, data, config)
}

export async function getInsuredAndDependant(id, config = {}) {
  const api = new ApiService()
  return await api.addAuthenticationHeader().get(`${path}/list/${id}`, config)
}

export async function getInsuredWithDependants(id, config = {}) {
  const api = new ApiService()
  return api.addAuthenticationHeader().get(`${path}/list/withdependant/${id}`, config)
}