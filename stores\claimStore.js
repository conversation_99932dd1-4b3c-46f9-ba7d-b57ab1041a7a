import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useClaimStore = defineStore(
  'claimStore',
  () => {
    const claimDetail = ref();
    function setClaimDetail(data) {
      claimDetail.value = data;
    }

    return {
      claimDetail,
      setClaimDetail,
    };
  },
  {
    persist: {
      enabled: true,
      storage: localStorage, // or sessionStorage
      paths: ['claimDetail'],
    },
  }
);
