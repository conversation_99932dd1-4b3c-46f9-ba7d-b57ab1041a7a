<script setup>
import * as session from '@/scripts/session';
import { useRouter } from 'vue-router';
import { onMounted, ref, watchEffect } from 'vue';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import BaseButton from '@/components/base/BaseButton.vue';
import CardBoxWidget from '@/components/cardbox/CardBoxWidget.vue';
import { chartData } from '@/components/charts/chartDatas';
import analyticsWidget from '@/components/analyticsWidget.vue';

import {
  mdiAccountMultiple,
  mdiCartOutline,
  mdiChartPie,
  mdiChartTimelineVariant,
  mdiReload,
} from '@mdi/js';
const router = useRouter();

const logout = () => {
  session.setUserIsLoggedIn(false);
  router.push('/login');
};
// TODO: Move the chart data and options to a separate file

const selectedFilter = ref('monthly');

onMounted(() => {
  updateChartData();
});
// Access the chart data and options
const BarOptions = ref(chartData.barOptions);
const series = ref(chartData.series);
const PolicyHolderOrCoverTypesSeries = ref(
  chartData.policyHolderOrCoverTypesSeries
);
const PolicyHolderOrCoverTypesOptions = ref(
  chartData.policyHolderOrCoverTypesOptions
);

// Paid Claims by Providers and Service Types
const PaidClaimsByProvidersAndServiceTypesSeries = ref(
  chartData.paidClaimsByProvidersAndServiceTypesSeries
);
const PaidClaimsByProvidersAndServiceTypesOptions = ref(
  chartData.paidClaimsByProvidersAndServiceTypesOptions
);

const TotalClaimsCreatedByInsurerOptions = ref(
  chartData.TotalClaimsCreatedByInsurerOptions
);
const TotalClaimsCreatedByInsurerSeries = ref(
  chartData.TotalClaimsCreatedByInsurerSeries
);

const TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderOptions = ref(
  chartData.TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderOptions
);
const TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderSeries = ref(
  chartData.TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderSeries
);
const TotalNumberOfInsuredPersonsByInstitution = ref(
  chartData.TotalNumberOfInsuredPersonsByInstitution
);

const TotalNumberOfInsuredPersonsByInstitutionOptions = ref(
  chartData.TotalNumberOfInsuredPersonsByInstitutionOptions
);

const TotalPremiumCollectedByPolicyholders = ref(
  chartData.TotalPremiumCollectedByPolicyholders
);
const TotalPremiumCollectedByPolicyholdersOptions = ref(
  chartData.TotalPremiumCollectedByPolicyholdersOptions
);

const updateChartData = () => {
  if (selectedFilter.value === 'monthly') {
    series.value[0].data = [30, 40, 45, 50, 49, 60, 70, 91, 80, 75, 65, 55];

    BarOptions.value = {
      chart: {
        id: 'claims-bar',
      },
      xaxis: {
        categories: [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ],
      },
    };
  } else if (selectedFilter.value === 'quarterly') {
    BarOptions.value = {
      chart: {
        id: 'claims-bar',
      },
      xaxis: {
        categories: ['Q1', 'Q2', 'Q3', 'Q4'],
      },
    };
    series.value[0].data = [115, 150, 165, 175];
  } else if (selectedFilter.value === 'annually') {
    BarOptions.value = {
      chart: {
        id: 'claims-bar',
      },
      xaxis: {
        categories: ['2019', '2020', '2021', '2022'],
      },
    };
    series.value[0].data = [78, 50, 65, 105];
  }
};
</script>
<template>
  <SectionMain class="">
    <SectionTitleLineWithButton :icon="mdiChartTimelineVariant" title="Overview" main>
      <BaseButton :icon="mdiReload" color="whiteDark" />
    </SectionTitleLineWithButton>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 p-2">
      <CardBoxWidget trend="12%" trend-type="up" color="text-emerald-500" :icon="mdiAccountMultiple" :number="90"
        label="Providers" />
      <CardBoxWidget trend="12%" trend-type="up" color="text-blue-500" :icon="mdiCartOutline" :number="200" prefix=""
        label="Institutions" />
      <CardBoxWidget trend="40%" prefix="ETB " trend-type="up" color="text-red-500" :icon="mdiChartTimelineVariant"
        :number="846573.00" suffix="" label="claims this month" />
    </div>
    <div class="bg-white p-4 rounded-2xl my-8 flex items-center justify-between">
      <h1 class="font-extrabold text-gray-900 dark:text-white md:text-2xl text-lg">
        <span class="text-transparent bg-clip-text bg-gradient-to-r to-purple-600 from-blue-400">Claims Analysis</span>
      </h1>
      <div class="">
        <!-- <label for="filter" class="mr-2">Filter:</label> -->
        <select id="filter" v-model="selectedFilter" @change="updateChartData"
          class="border-primary border-none shadow-sm rounded-lg text-primary">
          <option value="monthly" class="text-primary w-full rounded-lg">
            Monthly
          </option>
          <option value="quarterly">Quarterly</option>
          <option value="annually">Annually</option>
          <option value="semiannually">Semi-Annually</option>
        </select>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <analyticsWidget trend="12%" trend-type="up" color="text-emerald-500" :number="59"
        label="Number of Claims/week" />
      <analyticsWidget trend="12%" trend-type="up" color="text-blue-500" :icon="mdiCartOutline" :number="20000000"
        prefix="ETB " suffix="" label="Total Sum Assured" />
      <analyticsWidget trend="40%" trend-type="down" color="text-red-500" :icon="mdiChartTimelineVariant" :number="3554"
        suffix="" label="Average claims this month" />
    </div>

    <div class="w-full grid grid-cols-12 my-6 gap-8 text-center">
      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Periodic Total Paid
          Claims
        </span>
        <apexchart width="100%" height="90%" type="bar" :options="BarOptions" :series="series"></apexchart>
      </div>

      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Total Loss Ratio by
          Policyholder and Cover Types
        </span>
        <apexchart width="100%" height="90%" type="bar" :options="PolicyHolderOrCoverTypesOptions"
          :series="PolicyHolderOrCoverTypesSeries"></apexchart>
      </div>
      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Paid Claims by Providers
          and Service Types:
        </span>
        <apexchart width="100%" height="90%" type="bar" :options="PaidClaimsByProvidersAndServiceTypesOptions"
          :series="PaidClaimsByProvidersAndServiceTypesSeries"></apexchart>
      </div>
      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Total Claims Created by
          Insurer/Provider/Claimants
        </span>
        <apexchart type="pie" width="100%" height="90%" :options="TotalClaimsCreatedByInsurerOptions"
          :series="TotalClaimsCreatedByInsurerSeries"></apexchart>
      </div>

      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Total Number of Claims
          Submitted, Paid, and Rejected by
          Policyholder
        </span>
        <apexchart width="100%" height="90%" type="bar" :options="TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderOptions
      " :series="TotalNumberOfClaimsSubmittedPaidRejectedByPolicyholderSeries
      "></apexchart>
      </div>
      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Top 5 Institutions with
          the highest number of insured persons
        </span>
        <apexchart type="area" height="350" :options="TotalNumberOfInsuredPersonsByInstitutionOptions"
          :series="TotalNumberOfInsuredPersonsByInstitution"></apexchart>
      </div>
      <div class="col-span-6 h-[500px] bg-white p-8 rounded-2xl">
        <span class="text-lg text-primary text-ellipsis mb-3 flex items-center justify-center">Total Premium Collected
          by Policyholders:
        </span>
        <apexchart width="100%" height="90%" type="donut" :options="TotalPremiumCollectedByPolicyholdersOptions"
          :series="TotalPremiumCollectedByPolicyholders"></apexchart>
      </div>
    </div>
  </SectionMain>
</template>
