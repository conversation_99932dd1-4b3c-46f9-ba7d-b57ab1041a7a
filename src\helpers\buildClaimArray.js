export default function buildClaimsArray(
  claimsToBePayed,
  fromBank,
  toBank,
  chequeNumber,
  transactionNumber
) {
  const result = [];

  claimsToBePayed.forEach((claim) => {
    const claimUuid = claim.claimUuid;
    const providerUuid = claim.providerUuid;

    const claimObj = {
      claimUuid,
      providerUuid,
      fromBankUuid: fromBank,
      toBankUuid: toBank,
      checkNumber: chequeNumber,
      transactionNumber,
    };
    result.push(claimObj);
  });

  return result;
}
