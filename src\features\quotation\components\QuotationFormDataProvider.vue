<script setup>
import { useRoute } from 'vue-router'
import { useInstitutions } from '@/features/quotation/store/activeInstitutionsStore'
import { useApiRequest } from '@/composables/useApiRequest';
import { createInstitution, getInstitutionsId, updateInstitution } from '../api/institutionApi';
import { createQuotation, getQuotationById } from '../api/quotationApi';
import { getInsuredAndDependant } from '../api/insuredApi';
import { proceedToMembers as proceed } from '../api/payerInstitutionContract';
import { getAllPackages } from '@/features/quotation/api/packageApi'
import { ref } from 'vue';
import { issueQuotation, issuePremiumAdvice, updateQuotation, payQuotation, acceptQuotation } from '../api/quotationApi';
import { getPayerInstitutionContractById } from '../api/payerInstitutionContract';
import { importInsuredAndDependant } from '../api/insuredApi';
import { openModal } from '../../../modals'
import { toast } from '@/util/utils';
import { Status } from '@/util/enums';
import { getInsuredWithDependants } from '@/features/quotation/api/insuredApi'

const quotationReq = useApiRequest()

const props = defineProps({
  packages: {
    type: Boolean,
    default: true,
  },
  institution: {
    type: Boolean,
    default: true,
  },
  quotation: {
    type: Boolean,
    default: true,
  },
  contract: {
    type: Boolean,
    default: true
  }
})

const institutionStore = useInstitutions()

const route = useRoute()
const institutionUuid = ref(route.params?.id)
const quotationUuid = ref(route.params?.quotationId)
const payerInstitutionContractUuid = route.params?.payerInstitutionContractUuid
const members = ref(false)

const institutionReq = useApiRequest()
const insuredReq = useApiRequest()
const initReq = useApiRequest()
const membersReq = useApiRequest()

const institution = ref(institutionStore.institutions.find(el => el.institutionUuid == institutionUuid))
const quotation = ref({})
const contract = ref([])

async function getData() {
  const request = []
  let index = 0;
  const idx = {}
  if (props.institution && institutionUuid.value) {
    request.push(getInstitutionsId(institutionUuid.value))
    idx.institution = index++
  }
  if (props.quotation && quotationUuid.value) {
    request.push(getQuotationById(quotationUuid.value))
    idx.quotation = index++
  }
  if (props.contract && institutionUuid.value) {
    request.push(getPayerInstitutionContractById(quotationUuid.value, { status: Status.PENDING }))
    idx.contract = index++
  }

  if(payerInstitutionContractUuid) {
    request.push(getInsuredWithDependants(payerInstitutionContractUuid))
    idx.members = index++
  }

  idx.packages = index++
  try {
    const res = await Promise.all([...request, getAllPackages()])
    quotation.value = res?.[idx?.quotation]?.data
    institution.value = res?.[idx?.institution]?.data
    contract.value = res?.[idx?.contract]?.data
    members.value = res?.[idx?.members]?.data
    return {
      success: true,
      data: {
        institution: institution.value,
        quotation: quotation.value,
        packages: res?.[idx?.packages]?.data,
        contract: contract.value
      }
    }
  } catch (err) {
    return {
      success: false,
      data: '',
      error: err.message
    }
  }
}

initReq.send(
  () => getData(),
  res => {
    if (res.success) {
    }
  }
)

function addInstitution(values) {
  if (institutionReq.pending.value) return

  institutionReq.send(
    () => createInstitution(values),
    res => {
      toast(res.success, 'Added Succssfully', 'Couldnt Create. Try Again.')
      if (res.success) {
        institution.value = res.data
      }
    }
  )
}

function submitInstitution(id, values) {
  if (institutionReq.pending.value) return

  institutionReq.send(
    () => updateInstitution(id, values),
    res => {
      toast(res.success, 'Updated Succssfully', 'Couldnt Update. Try Again.')
      if (res.success) {
        institution.value = res.data
      }
    }
  )
}

function addQuotation(values, cb = f => f) {
  if (quotationReq.pending.value) return
  quotationReq.send(
    () => createQuotation(values),
    res => {
      toast(res.success, 'Created Successfully', 'Couldnt Create. Try Again.')
      if (res.success) {
        quotation.value = res.data
      }
      cb(res)
    }
  )
}

function submitQuotation(id, values) {
  if (quotationReq.pending.value) return
  quotationReq.send(
    () => updateQuotation(id, values),
    res => {
      toast(res.success, 'Updated Succssfully', 'Couldnt Update. Try Again.')
      if (res.success) {
        quotation.value = res.data
      }
    }
  )
}

function submitIssueQuotation(id, values, cb = f => f) {
  if (quotationReq.pending.value) return

  openModal('Confirmation', {
    title: 'Issue Quotation',
    message: 'Are You sure you want to issue this quotation?'
  }, (res) => {
    if (res) {
      quotationReq.send(
        () => issueQuotation(id, values),
        res => {
          console.log('%c here maje', 'color: red')
          cb(res)
          toast(res.success, 'Issued Successfully', 'Couldnt Issue Quotation. Try Again.')
          if (res.success) {
            quotation.value = res.data
          }
        }
      )
    }
  })
}

function onSubmitAccept(id, values) {
  if (quotationReq.pending.value) return
  openModal('Confirmation', {
    title: 'Accept Quotaion',
    message: 'Are you sure you want to accept this quotation?'
  }, (res) => {
    if (res) {
      quotationReq.send(
        () => acceptQuotation(id, values),
        res => {
          toast(res.success, 'Successfull', 'Couldnt Execute Action. Try Again.')
          if (res.success) {
            quotation.value = res.data
          }
        }
      )
    }
  })
}

function onPayQuotaion(id) {
  if (quotationReq.pending.value) return
  openModal('Confirmation', {
    title: 'Premium Payment',
    message: 'Are you sure you want to precced to premium payment?'
  }, (res) => {
    if (res) {
      quotationReq.send(
        () => payQuotation(id),
        res => {
          toast(res.success, 'Successfully Paid', 'Couldnt Pay. Try Again.')
          if (res.success) {
            quotation.value = res.data
          }
        }
      )
    }
  })
}

function premiumAdvice(id) {
  if (quotationReq.pending.value) return

  openModal('Confirmation', {
    title: 'Issue Premium Advice',
    message: 'Are you sure you want to issue this premium?'
  }, (res) => {
    if (res) {
      quotationReq.send(
        () => issuePremiumAdvice(id),
        res => {
          toast(res.success, 'Successfully Issued', 'Couldnt Issue. Try Again.')
          if (res.success) {
            quotation.value = res.data
          }
        }
      )
    }
  })
}

function proceedToMembers(data) {
  if (quotationReq.pending.value) return
  // openModal('Confirmation', {
  //   title: 'Process To Policy Setup',
  //   message: 'Are You sure?'
  // }, (res) => {
  //   if (res) {
  quotationReq.send(
    () => proceed({ ...data, quotationUuid: quotationUuid.value }),
    res => {
      toast(res.success, 'Successfull', 'Couldnt Execute Action. Try Again.')
      if (res.success) {
        contract.value = [res.data]
      }
    }
  )
  //   }
  // })
}

function importInsured(query, data, config = {}) {
  const fd = new FormData()
  fd.append('file', data)
  insuredReq.send(
    () => importInsuredAndDependant(query, fd, {
      ...config,
      headers: {
        'Content-type': 'multipart/form-data'
      }
    }),
    res => {
      toast(res.success, 'Successfully imported', 'Couldnt import. Try Again.')
    }
  )
}

</script>
<template>
  <slot
    :id="institutionUuid"
    :premiumAdvice="premiumAdvice"
    :quotationId="quotationUuid"
    :institution="institution" :quotation="quotation"
    :members="members"
    :payQuotaion="onPayQuotaion"
    :packages="initReq.response.value?.packages" :contract="contract?.length ? contract : ''"
    :onSubmitAccept="onSubmitAccept" :submitIssueQuotation="submitIssueQuotation" :submitQuotation="submitQuotation"
    :submitInstitution="submitInstitution" :addInstitution="addInstitution" :addQuotation="addQuotation"
    :importInsured="importInsured" :proceedToMembers="proceedToMembers" :initPending="initReq.pending.value"
    :pending="quotationReq.pending.value || institutionReq.pending.value" :error="initReq.error.value" />
</template>