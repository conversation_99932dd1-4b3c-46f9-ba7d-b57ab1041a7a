import ApiService from '@/service/ApiService'
import { getQueryFormObject } from '@/util/utils'

const path = '/payer-institution-contract'

export async function proceedToMembers(data, config = {}) {
  const api = new ApiService()
  return await api.addAuthenticationHeader().post(path, data, config)
}

export async function getPayerInstitutionContractById(id, query = {}, config = {}) {
  const api = new ApiService()
  const qs = getQueryFormObject(query)
  return await api.addAuthenticationHeader().get(`${path}/quotation-group/list/${id}${qs}`, config)
}

export async function getPayerInstitutionContract(id, query = {}, config = {}) {
  const api = new ApiService()
  const qs = getQueryFormObject(query)
  return await api.addAuthenticationHeader().get(`${path}/${id}${qs}`, config)
}