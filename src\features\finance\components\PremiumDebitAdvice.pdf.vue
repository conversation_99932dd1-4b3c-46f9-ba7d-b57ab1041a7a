<script setup>
import pdfMake from 'pdfmake/build/pdfmake'
import pdfFonts from 'pdfmake/build/vfs_fonts'
import { toWords, getBgbase64 } from '@/util/utils'
import { computed, ref } from 'vue';
import { formatCurrency, formatNumber, secondDateFormat } from '@/util/utils'
import { PlanTypes } from '@/util/enums';

pdfMake.vfs = pdfFonts.pdfMake.vfs

const props = defineProps({
  packages: {
    type: Array,
    required: true
  },
  institution: {
    type: Object,
    required: true
  },
  quotation: {
    type: Object,
    required: true
  },
  members: {
    type: Array,
    required: true
  }
})

const quotation = props.quotation.quoatedServices.map(el => {
  return {
    ...el,
    packageName: props.packages.find(pack => pack.packageUuid == el.packageUuid)?.packageName
  }
})
const group = Object.groupBy(quotation, ({ packageName }) => packageName)

const femaleGroup = Object.groupBy(props.packages, ({ gender }) => gender == 'FEMALE')
const planGroup = Object.groupBy(props.quotation.quoatedServices, ({ planType }) => planType)

const femaleonlyPackages = femaleGroup.true.map(el => el.packageUuid)

const individualPlan = (planGroup?.[PlanTypes.Individual] || []).filter(el => !femaleonlyPackages.includes(el.packageUuid))
const individualPlanPerPackage = individualPlan.reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: el.coverage * el.numberOfInsured,
      type: [el]
    })
  }
  return state
}, [])

const sharedlPlanPerPackage = (planGroup?.[PlanTypes.Shared] || []).reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += el.coverage * el.numberOfInsured * el.description
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: el.coverage * el.numberOfInsured * el.description,
      type: [el]
    })
  }
  return state
}, [])

const familyPlanPerPackage = (planGroup?.[PlanTypes.Family] || []).reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: (el.coverage * el.numberOfInsured),
      type: [el]
    })
  }
  return state
}, [])

const femalePlan = Object.values(planGroup).flat().filter(el => femaleonlyPackages.includes(el.packageUuid))
const femalePlanPerPackage = femalePlan.reduce((state, el, idx) => {
  const found = state.find(pack => pack.packageUuid == el.packageUuid)
  if (found) {
    found.coverage += (el.coverage * el.numberOfInsured)
    found.type.push(el)
  } else {
    state.push({
      name: getPackageName(el.packageUuid),
      packageUuid: el.packageUuid,
      coverage: (el.coverage * el.numberOfInsured),
      type: [el]
    })
  }
  return state
}, [])

console.log(femalePlanPerPackage)
function getPackageName(id) {
  return props.packages.find(el => el.packageUuid == id)?.packageName
}

function numberOfInsured() {
  const group = Object.groupBy(props.quotation.quoatedServices, (({ packageUuid }) => packageUuid))
  return group[Object.keys(group)[0]].reduce((sum, el) => sum += el.numberOfInsured, 0)
}

function getGrandTotal(name) {
  const dependantsLength = props.members.reduce((sum, el) => sum += el.dependants.length, 0)
  return props.members.reduce((sum, member) => {
    const found = group[name]
      .find(el => el.description == member.dependants.length)
    if (!found) return sum
    return sum += found?.coverage
  }, 0)
}

function getGrandTotalPremium() {
  const dependantsLength = props.members.reduce((sum, el) => sum += el.dependants.length, 0)
  return props.members.reduce((sum, member) => {
    const premium = Object.keys(group)
      .reduce((sum, key) => {
        const res = group[key].find(el => el.description == member.dependants.length)
        return sum += res?.premium || 0
      }, 0)
    return sum += premium
  }, 0)
}
const file = ref()
async function getPdf() {
  const bg = await getBgbase64('/crtificate_header_anbessa.png')

  const docDefinition = {
    content: [
      {
        bold: true,
        fontSize: 30,
        decoration: 'underline',
        alignment: 'center',
        text: 'Premium Debit Advice'
      },
      {
        marginTop: 8,
        style: ['defaultTopMargin', { fontSize: 12 }],
        alignment: 'right',
        stack: [
          {
            style: { bold: true },
            decoration: 'underline',
            text: `Payment Order Date: ${secondDateFormat(props.quotation?.poDate)}`,
          },
        ]
      },
      {
        marginTop: 8,
        fontSize: 13,
        fontWeight: '500',
        text: 'Bill To: '
      },
      {
        marginLeft: 12,
        marginTop: 8,
        bold: true,
        text: 'Customer: ' + props.institution.institutionName
      },
      {
        marginLeft: 12,
        marginTop: 4,
        bold: true,
        text: 'Address: ' + props.institution.address3 + ', ' + props.institution.country
      },
      {
        marginTop: 8,
        style: { fontSize: 14, bold: true },
        text: 'Dear Sir/Madam'
      },
      {
        marginTop: 8,
        text: [
          'Please effect a Renewal/',
          { text: 'New', bold: true, decoration: 'underline' },
          '/Additional premium payment for your insurance coverage as illustrated here under:'
        ]
      },
      {
        marginTop: 8,
        table: {
          widths: [20, 'auto', 'auto', '*', 'auto'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Types of Policy', style: 'tableHeader' }, { text: 'Period', style: 'tableHeader' }, { text: 'Quotation Number' }, { alignment: 'right', text: 'Premium (ETB)', style: 'tableHeader' }],
            [1, 'Health Insurance', 'TBA', props.quotation.quotationCode, { alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0)) }],
            [{
              colSpan: 4, text: [
                `Reason: New/Initial Premium.\n
                  Reason for: Additional/
                `,
                {
                  bold: true,
                  text: '/New/'
                },
                'Renewal'
              ]
            }, {}, {}, {}, {}],
            [{
              colSpan: 3, fontSize: 11.5, text: `Bank Account Information:
              Bank Name: Family Bank S.C
              Account Name/Holder: Family Insurance S.C.
              Bank Branch Name: Wuhalimat Branch
              Tell: +251-11-618-9021 Addis Ababa
              Account Number: **************
              Swift Code:  AWETAA`}, {}, {}, {}, {}],
            [{ colSpan: 4, alignment: 'right', text: 'Revenue Stamp' }, {}, {}, {}, { style: { bold: true }, alignment: 'right', text: formatCurrency(5) }],
            [{ colSpan: 4, alignment: 'right', text: 'Grand Total' }, {}, {}, {}, { style: { bold: true, fontSize: 14 }, alignment: 'right', text: formatCurrency(props.quotation?.quoatedServices?.reduce((sum, el) => sum += el.premium, 0) + 5) }],
          ]
        }
      },
      {
        marginTop: 8,
        text: [
          'Therefore, you are hereby requested to settle a total premium of ',
          ` Birr ${formatNumber(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)}`,
          {
            bold: true,
            text: ` (${toWords(props.quotation.quoatedServices.reduce((sum, el) => sum += el.premium, 0) + 5)})`
          },
          ' to the above-stated bank account at the shortest possible time.'
        ],
        pageBreak: 'after'
      },
      {
        pageMargins: [20, 10, 20, 10],
        style: { fontSize: 6 },
        table: {
          widths: ['auto', 'auto', 'auto', 10, 12, ...Array.from(Object.keys(group), () => 'auto'), '*'],
          body: [
            [{ text: 'S.N', style: 'tableHeader' }, { text: 'Employee Name', style: 'tableHeader' }, { text: 'Relationship', style: 'tableHeader' }, { text: 'Gender', style: 'tableHeader' }, { text: 'Size', style: 'tableHeader' }, ...Object.keys(group).map(el => ({ text: el, style: 'tableHeader' })), { alignment: 'right', text: 'Total Annual Premium', style: 'tableHeader' }],
            ...props.members.reduce((members, member, idx) => {
              const row = []
              row.push(idx + 1)
              row.push(`${member.insuredTitle} ${member.firstName} ${member.fatherName} ${member.grandFatherName}`)
              row.push('Employee')
              row.push(member.gender == 'Male' ? 'M' : 'F')
              row.push(member.dependants.length)
              Object.keys(group)
                .forEach(key => {
                  const res = group[key].find(el => el.description == member.dependants.length)
                  row.push(res?.coverage > 0 ? formatCurrency(res?.coverage) : 0)
                })
              row.push({
                alignment: 'right', text: formatCurrency(Object.keys(group)
                  .reduce((sum, key) => {
                    const res = group[key].find(el => el.description == member.dependants.length)
                    return sum += res?.premium || 0
                  }, 0))
              })
              members.push(row)
              member.dependants.forEach((dep, index) => {
                const row = []
                row.push(`${idx + 1}.${index + 1}`)
                row.push(`${dep.dependantFirstName} ${dep.dependantFatherName} ${dep.dependantGrandFatherName}`)
                row.push(dep.relationship)
                row.push(member.dependantGender == 'Male' ? 'M' : 'F')
                row.push('')
                Object.keys(group)
                  .forEach(key => {
                    const res = group[key].find(el => el.description == member.dependants.length)
                    row.push('')
                  })
                row.push('')
                members.push(row)
              })
              return members
            }, []),
            [
              {},
              { text: 'Total...', bold: true },
              {}, {}, {},
              ...Object.keys(group)
                .map(el => ({ bold: true, text: formatCurrency(getGrandTotal(el)) })),
              { bold: true, alignment: 'right', text: formatCurrency(getGrandTotalPremium()) }
            ]
          ]
        }
      }
    ],
    background: [
      {
        image: bg,
        width: 600
      }
    ],
    pageMargins: [30, 140, 30, 120],
    styles: {
      defaultTopMargin: {
        margin: [0, 0, 0, 0]
      },
      tableHeader: {
        bold: true
      }
    }
  }

  pdfMake.createPdf(docDefinition).getBlob(blob => {
    file.value = URL.createObjectURL(blob)
  })
}

getPdf()
</script>
<template>
  <embed type='application/pdf' :src='file' frameborder="0" width="100%" height="800" />
</template>