import apiService from "@/service/ApiService";

const path = "/dependant";

export async function addDependants(data, config = {}) {
  const api = new ApiService();
  return api.addAuthenticationHeader().post(path, data, config);
}

export async function getDependants(id, status, config = {}) {
  const api = new ApiService();
  return api
    .addAuthenticationHeader()
    .get(`${path}/list/${id}?status=${status}`, config);
}
