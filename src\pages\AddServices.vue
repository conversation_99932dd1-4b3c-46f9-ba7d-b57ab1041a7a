<script setup>
import { useRouter, useRoute } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import { makeAuthenticatedRequest } from '@/scripts/api'
import { mdiSecurity, mdiPlus } from '@mdi/js';
import { onMounted, ref, computed, toRefs, watch } from 'vue';
import Modal from '@/components/modal.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { useToast } from 'vue-toastification';
import Loader from '@/components/loader/loader.vue';
import BaseButton from '@/components/base/BaseButton.vue';

const toast = useToast()
const router = useRouter()
const route = useRoute()

const props = defineProps({
    payerProviderContractUuid: {
        type: String,
        required: true,
    }
});

const { payerProviderContractUuid } = toRefs(props)

const isLoading = ref(false)
const services = ref([])
const selectAll = ref(false)
const selected = ref([])
const search = ref(localStorage.getItem('searchTerm') || '')
const Category = ref('')
const subCategory = ref('')
const nameService = ref('')
const providerName = ref('')
const showInput = ref(false)
const showInput1 = ref(false)
const showInput2 = ref(false)
const showPrice = ref(false)

watch(search, (newSearchTerm) => {
    localStorage.setItem('searchTerm', newSearchTerm);
    if (newSearchTerm === '') {
        fetchServices();
    }
});

providerName.value = route.params.providerName

const toggleButton = () => {
    showInput.value = true
}
const toggleButton1 = () => {
    showInput1.value = true
}
const toggleButton2 = () => {
    showInput2.value = true
}
const togglePrice = () => {
    showPrice.value = true
}

const searchServices = async (search) => {
    isLoading.value = true
    try {
        await makeAuthenticatedRequest({
            method: 'GET',
            url: `/api/claimconnect/service/search?search=${search}&page=1&limit=25`
        }).then((data) => {
            isLoading.value = false
            services.value = data.map(service => ({
                ...service,
                price: 0,
            }));
        })

    } catch (error) {
        isLoading.value = false;
        toast.error(error)
    }
}

const addServices = async (service) => {
    isLoading.value = true;
    try {
        await makeAuthenticatedRequest({
            method: 'POST',
            url: '/api/claimconnect/eligible-service',
            data: [
                {
                    "payerProviderContractUuid": payerProviderContractUuid.value,
                    "serviceListUuid": service.serviceUuid
                }
            ]
        }).then((data) => {
            isLoading.value = false
            toast.success(data.message)
        })

        if (service.price != 0) {
            postServicePrice(service.serviceUuid, service.price)
        }

    } catch (error) {
        isLoading.value = false;
        toast.error(error.message)
    }
}

const postServicePrice = async (service, price) => {
    isLoading.value = true;
    try {
        await makeAuthenticatedRequest({
            method: 'POST',
            url: '/api/claimconnect/service/price',
            data: {
                "serviceUuid": service,
                "serviceContractUuid": payerProviderContractUuid.value,
                "value": price,
                "status": "Active"
            }
        }).then((data) => {
            isLoading.value = false
            toast.success(data.message)
        })
    } catch (error) {
        isLoading.value = false;
        toast.error(error.message)
    }
}

const postSelectedServices = async () => {
    for (const service of selected.value) {
        if (service) {
            await addServices(service);
        } else {
            toast.error('No Service is Selected To Be Added to The Contract!')
            break;
        }
    }
    router.back()
}

const fetchServices = async () => {
    isLoading.value = true
    try {
        if (search.value) {
            searchServices(search.value)
        } else {
            await makeAuthenticatedRequest({
                method: 'GET',
                url: '/api/claimconnect/service/all?page=1&limit=25'
            }).then((data) => {
                isLoading.value = false
                services.value = data.map(service => ({
                    ...service,
                    price: 0,
                }));
            })
        }
    } catch (error) {
        isLoading.value = false;
        toast.error(error)
    }
}

const select = () => {
    selected.value = []
    if (!selectAll.value) {
        for (let i in services.value) {
            selected.value.push(services.value[i].serviceUuid)
        }
    }
}

const filteredItems = computed(() => {
    if (!Category.value && !subCategory.value && !nameService.value) {
        return services.value
    } else {
        return services.value.filter(item =>
            item.category.toLowerCase().includes(Category.value.toLowerCase()) &&
            item.subCategory.toLowerCase().includes(subCategory.value.toLowerCase()) &&
            item.item.toLowerCase().includes(nameService.value.toLowerCase())
        )
    }
})

onMounted(() => {
    fetchServices()
})

</script>

<template>
    <div class="w-full h-max">
            <SectionMain>
                <SectionTitleLineWithButton :icon="mdiSecurity" :title="'Add' + ' ' + 'Services'" main>
                    <BaseButton :icon="mdiPlus" color="whiteDark" @click="postSelectedServices"
                        label="Add Selected Services" />
                </SectionTitleLineWithButton>
                <div class="w-full flex justify-start">
                    <form class="w-[30%] mb-2">
                        <label for="default-search"
                            class="mb-1 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                        <div class="relative">
                            <input type="text" v-model="search" id="default-search"
                                class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Search for Services here . . ." required />
                            <button type="submit" @click="fetchServices"
                                class="text-white absolute end-2.5 bottom-2.5 bg-primary/70 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
                <div class="mt-4 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col"
                                                class="px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('payerName')">S/N</th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="toggleButton">
                                                <div>
                                                    <input v-if="showInput" type="text" v-model="Category"
                                                        placeholder="Service Category"
                                                        class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Category
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('payerPhone')">
                                                <div @click="toggleButton1">
                                                    <input v-if="showInput1" type="text" v-model="subCategory"
                                                        placeholder="Service Sub Category"
                                                        class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Sub Category
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('payerName')">
                                                <div @click="toggleButton2">
                                                    <input v-if="showInput2" type="text" v-model="nameService"
                                                        placeholder="Service Name" class="h-4 box-content rounded-md w-25">
                                                    <p v-else>
                                                        Service Name
                                                    </p>
                                                </div>
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                                @click="sort('endDate')">Unit Price</th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                <input type="checkbox" v-model="selectAll" @click="select">
                                                <i class="form-icon"></i>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200" v-if="filteredItems != []">
                                        <tr v-for="(service, index) in filteredItems"
                                            :key="service.payerProviderContractUuid" class="h-2 box-content"
                                            :style="{ backgroundColor: index % 2 === 0 ? '#f0f0f0' : '#ffffff' }">
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">{{
                                                index + 1 }}</td>
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">{{
                                                service.category }}</td>
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">{{
                                                service.subCategory }}</td>

                                            <td
                                                class="whitespace-nowrap py-1 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                {{ service.item }}</td>
                                            <td class="whitespace-nowrap px-3 py-1 text-sm text-gray-500">
                                                <div @click="togglePrice">
                                                    <input v-if="showPrice" type="number" step="0.1" placeholder="0.0 ብር"
                                                        v-model="service.price" class="w-20 h-4 box-content rounded-md">
                                                    <p v-else>
                                                        click here to add price
                                                    </p>
                                                </div>
                                            </td>
                                            <td
                                                class="whitespace-nowrap py-1 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                <input type="checkbox" :value="service" v-model="selected">
                                                <i class="form-icon"></i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <p v-if="filteredItems.length === 0"
                                    class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">No Items
                                    Were Found</p>
                            </div>
                        </div>
                    </div>
                </div>
            </SectionMain>
    </div>
</template>