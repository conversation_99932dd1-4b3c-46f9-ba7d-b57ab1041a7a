import api from "@/scripts/api";
import { handleError } from "vue";
import { useToast } from "vue-toastification";
const toast = useToast();
export default {
  async getAllPackageRatePerFamily() {
    try {
      const response = await api.get("/package");
      console.log("from response", response);
      return response;
    } catch (error) {
      return handleError(error);
    }
  },

  async addPackageRatePerFamiliy(data) {
    let pdata = {};
    try {
      await api.post("/familybenefitrange", data).then((response) => {
        toast.success("Family benefit range saved successfully");
        pdata = response;
      });

      return pdata;
    } catch (error) {
      toast.error("Oops some thing eror", error);
      return handleError(error);
    }
  },

  async getPackageById(id) {
    try {
      const response = await api.get(
        `/familybenefitrange/getRangesPerCover/${id}`
      );
      //  .then((response) => {
      //   console.log('familiy benefit range range response',response)

      //  });
      return response;
    } catch (error) {
      return handleError(error);
    }
  },
  async updatePackage(data, id) {
    let rdata = [];
    try {
      const response = await api
        .put(`/familybenefitrange/update`, data)
        .then((response) => {
          rdata = response;
          toast.success("rate for family updated successfully");
        });

      return rdata;
    } catch (error) {
      toast.error("Oops! some thing error");
      return handleError(error);
    }
  },

  //  update package status
  async updatePackageStatus(data, id) {
    let rdata = [];
    try {
      const response = await api
        .put(`/familybenefitrange/update`, data)
        .then((response) => {
          rdata = response;
          toast.success("rate for family updated successefuly");
        });

      return rdata;
    } catch (error) {
      toast.error("Oops! some thing error");
      return handleError(error);
    }
  },

  async deletePackagePerfamilyById(id) {
    try {
      console.log("service famility", id);
      const response = await api.delete(`/familybenefitrange/${id}`);
      toast.success("deleted successfully");
      return true;
    } catch (error) {
      toast.error("Oops! something error", error);
      return false;
    }
  },
};
