<script setup>
import { getInsuredAndDependant } from '@/features/quotation/api/insuredApi'
import { useApiRequest } from '@/composables/useApiRequest'
import TableWrapper from '@/components/TableWrapper.vue';
import { useRoute } from 'vue-router';
import { formatCurrency, secondDateFormat } from '@/util/utils';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { mdiBullhorn } from '@mdi/js';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { openModal } from '@/modals';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { ref } from 'vue';

const route = useRoute()

const id = route.params.id
const insuredReq = useApiRequest()

insuredReq.send(
  () => getInsuredAndDependant(id),
  res => {
    console.log(res)
  }
)

const search = ref()
</script>
<template>
  <SectionMain class="flex flex-col gap-2">
    <SectionTitleLineWithButton :icon="mdiBullhorn" title="Insured And Dependant" main>
    </SectionTitleLineWithButton>
    <div>
      <input class="rounded-md border-gray-300" placeholder="Search..." v-model="search" />
    </div>
    <TableWrapper :Fallback="TableRowSkeleton" :pending="insuredReq.pending.value" :defaultAction="false" :headers="{
      head: ['full name', 'gender', 'DOB', 'phone', 'modify'],
      row: ['fullname', 'gender', 'dob', 'phone',]
    }" :rows="insuredReq.response.value?.reduce((state, el) => {
        el.fullname = `${el.title} ${el.firstName} ${el.fatherName} ${el.grandFatherName}`
        el.dob = secondDateFormat(el.birthDate)
        el.premium = formatCurrency(el.premium)
        state.push(el)
        return state
      }, []) || []">
      <template #actions="{ row }">
        <DropdownBtn @click="openModal('AddDependantForm', row.insuredUuid)">
          Dependants
        </DropdownBtn>
      </template>
    </TableWrapper>
  </SectionMain>
</template>