<script setup>
  import FormParent from '@/components/FormParent.vue';
  import DependantForm from './DependantForm.vue'
  import { useApiRequest } from '@/composables/useApiRequest'
  import { addDependants } from '../../api/dependantApi'
  import { ref } from 'vue'
  import { genId, secondDateFormat } from '@/util/utils'
  import TableWrapper from '@/components/TableWrapper.vue';
  import FormSubmitButton from '@/components/FormSubmitButton.vue';
  import DependatsDataProvider from '../DependatsDataProvider.vue';
  import Loader from '@/components/loader/loader.vue';

  const props = defineProps({
    insuredUuid: {
      type: String,
      requried: true
    }
  })

  const dependantReq = useApiRequest()
  
  const dependants = ref([])
  const dependant = ref({})

  function append({values, reset}) {
    console.log(values)
    if(dependant.value) {
      const idx = dependants.value.findIndex(el => el.id == dependant.value.id)
      if(idx > -1) {
        dependants.value.splice(idx, 1,{id: genId.next().value, ...values})
      }  
    } else {
      dependants.value.push({id: genId.next().value, ...values})
    }
    reset()
    dependant.value = {}
  }

  function remove(id) {
    dependants.value = dependants.value.filter(el => el.id != id)
  }
</script>
<template>
  <FormParent class="w-[50rem] bg-white rounded-md" title="Add Dependants">
    <DependatsDataProvider v-slot="{ dependants, pending }" :insuredUuid="insuredUuid">
      <template v-if="!pending">
        <DependantForm
          btn-text="Add"
          :on-submit="append"
          :dependant="dependant"
        >
          <template #submitBtn="{ submit }">
            <button class="col-span-2 bg-primary text-white py-1 rounded-md" @click.prevent="submit(append)">Add</button>
          </template>
        </DependantForm>
        <div v-if="dependants?.length" class="flex flex-col gap-2">
          <p class="text-lg font-semibold border-y py-1">Dependants</p>
          <TableWrapper
            @action:edit="(row) => dependant = row"
            @action:delete="(row) => remove(row.id)"
            :headers="{
              head: ['full name', 'relationship', 'phone', 'birthDate', 'modify'],
              row: ['fullname', 'relationship', 'phone', 'dob'],
            }"
            :rows="dependants.reduce((state, el) => {
              el.fullname = `${el.title ? el.title : ''} ${el.firstName} ${el.fatherName} ${el.grandFatherName}`
              el.dob = secondDateFormat(el.birthDate)
              state.push(el)
              return state
            }, [])"
          >
          </TableWrapper>
        </div>
        <FormSubmitButton
          class="flex-shrink-0"
          v-if="dependants?.length"
          btn-text="Submit"
        ></FormSubmitButton>
      </template>
      <div v-else class="flex w-full justify-center h-56">
        <Loader />
      </div>
    </DependatsDataProvider>
  </FormParent>
</template>