<script setup>
  import { getQuotations } from '@/features/quotation/api/quotationApi'
  import { QuotationStatus, Status } from '@/util/enums';
  import SearchWithPagination from '@/components/SearchWithPagination.vue'
  
  const props = defineProps({
    status: {
      type: String,
      default: QuotationStatus.PENDING
    },
    search: String,
    store: Object
  })

</script>

<template>
  <SearchWithPagination :store="store" v-slot="{ data, pending }" :search="search" :cb="(data, config) => getQuotations({...data, status: props.status}, config)">
    <!-- {{ console.log(data.sort((a, b) => new Date(a.createdDate) > new Date(b.createdDate))) }} -->
    <slot 
      :institutions="data"
      :pending="pending"
    />
  </SearchWithPagination>
</template>