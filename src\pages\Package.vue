<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import PackageList from '@/components/tables/PackageList.vue';
import Modal from "@/components/modal.vue";
import CreatePackage from "@/components/forms/CreatePackage.vue";
import { useRouter } from "vue-router";
import { ref, onMounted, watch } from "vue";
import { mdiPlus, mdiPackage } from "@mdi/js";
import { storeToRefs } from 'pinia';
import { usePackageStore } from '../../stores/packageStore';
import BaseButton from '@/components/base/BaseButton.vue';
import loader from "@/components/loader/loader.vue";
const packageStore = usePackageStore();
const { packageList, PackageRange, pending } = storeToRefs(packageStore);

const router = useRouter();
const packageRanges = ref([]);
const open = ref(false);

// const addPackage = () => {
//   router.push('/package/addpackage');
// };

const closeModal = () => {
  open.value = false;
};

const fetchData = async () => {
  await packageStore.getAllPackages();
  console.log('package list', packageList.value);
  packageRanges.value = packageList.value;
};

onMounted(() => {
  fetchData();
});

watch(packageList, () => {
  packageRanges.value = packageList.value;
});
</script>

<template>
  <Modal :open="open" @close="open = !open" title="Add New Coverage" :icon="mdiPackage">
    <CreatePackage @close-modal="closeModal" />
  </Modal>

  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiPackage" title="Coverages" class="">
      <BaseButton :icon="mdiPlus" title="Coverage " color="whiteDark" @click="open = !open" label="Add Coverage "
        class="mx-4 my-2" />
    </SectionTitleLineWithButton>


    <PackageList :packageRanges="packageRanges" :pending="pending"></PackageList>
    <div v-if="pending" class="flex items-center justify-center h-[40vh]">
      <loader />
    </div>
  </SectionMain>
</template>
