<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import QuotationForm from '@/features/quotation/components/form/QuotationForm.vue'
import QuotationFormDataProvider from '@/features/quotation/components/QuotationFormDataProvider.vue'
import { toast } from '@/util/utils';
import { createQuotation, issueQuotation } from '../../api/quotationApi';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import Loader from '@/components/loader/loader.vue';
import { createInstitution } from '../../api/institutionApi';
import { QuotationStatus } from '@/util/enums';
//import { openModal } from '@customizer/modal-x';
import { mdiBullhorn } from '@mdi/js';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';

</script>
<template>
  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiBullhorn" title="Quotation" main>
    </SectionTitleLineWithButton>
    <QuotationFormDataProvider :quotation="false"
      v-slot="{ contract, addInstitution, submitIssueQuotation, initPending, onSubmitAccept, addQuotation, submitQuotation, institution, quotation, packages, pending }">
      {{ console.log(quotation) }}
      <QuotationForm v-if="!initPending" institution-title="Add Institution" quotation-title="Register new Quotation"
        :pending="pending" btn-text-institution="Add Institution"
        :btnTextQuotation="quotation ? 'Save Quotation' : 'Save Quotation'" :packages="packages"
        :showInstitutionBtn="!institution" :on-submit-institution="addInstitution" :quotation="quotation"
        :showProceedBtn="false" :on-submit-quotation="!quotation ? addQuotation : (values) => submitQuotation(quotation?.quotationUuid, values, (res) => {
          if (res.success) {
            $router.push('/Quotation/Issued-Quotations')
          }
        })" :on-submit-quotation-issue="(values) => submitIssueQuotation(values.quotationUuid, values, (res) => {
  console.log(res)
  if (res.success) {
    $router.push('/Quotation/Issued-Quotations')
  }
})" :on-submit-accept-quotation="(values) => onSubmitAccept(values.quotationUuid, values)"
        :show-issue-btn="quotation?.status == null || quotation?.status == QuotationStatus.PENDING"
        :showAcceptBtn="quotation?.status == QuotationStatus.ISSUED"
        :show-quotation-btn="!quotation?.status || quotation?.status == QuotationStatus.PENDING"
        :institution="institution"></QuotationForm>
      <div v-else class="h-[20rem] grid place-items-center">
        <Loader />
      </div>
    </QuotationFormDataProvider>
  </SectionMain>
</template>