<script setup>
import { mdiFilePdfBox, mdiPlus, mdiSendClock } from '@mdi/js';
import QuotationPage from './QuotationPage.vue';
import SavedQuotationsDataProvider from '../components/SavedQuotationsDataProvider.vue';
import TableWrapper from '@/components/TableWrapper.vue';
import DropdownBtn from '@/components/DropdownBtn.vue';
import { QuotationStatus } from '@/util/enums';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { Icon } from '@iconify/vue';
import { replicateQuotation } from '@/features/quotation/api/quotationApi'
import { useApiRequest } from '@/composables/useApiRequest'
import { toast } from '@/util/utils';
import { openModal } from '@/modals';

const replicateReq = useApiRequest()

function replicate(id) {
  openModal('Confirmation', {
    message: 'Are you Sure you need to replicate?'
  }, res => {
    if (res) {
      replicateReq.send(
        () => replicateQuotation(id),
        res => {
          toast(res.success, 'Siccessfully Replicated', 'Couldnt Replicate. Try Again')
        }
      )
    }
  })
}
</script>
<template>
  <QuotationPage :headers="{ head: ['Quotation Number', 'Issued date'], row: ['quotationCode', 'issuedDate'] }"
    :status="QuotationStatus.ISSUED" :DataProvider="SavedQuotationsDataProvider" class="flex flex-col"
    :icon="mdiSendClock">
    <template #actions="{ row }">
      <DropdownBtn @click="$router.push('/view_quotation/' + row?.institutionUuid + '/' + row?.quotationUuid)">
        View Quotation
      </DropdownBtn>
      <DropdownBtn @click="replicate(row?.quotationUuid)">
        Revise Quotation
      </DropdownBtn>

    </template>
    <template #additionalBtns="{ row }">
      <button @click="$router.push('/quotation_sample/' + row?.institutionUuid + '/' + row?.quotationUuid)"
        class="w-12 h-10 flex items-center justify-center rounded-md border bg-primary border-gray-300">
        <Icon icon="mdi:file-pdf-box" class="text-white text-2xl" />
      </button>
    </template>
  </QuotationPage>
</template>