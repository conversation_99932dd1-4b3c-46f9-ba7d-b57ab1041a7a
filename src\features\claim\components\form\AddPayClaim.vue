<script setup>
  import FormParent from '@/components/FormParent.vue';
  import { useApiRequest } from '@/composables/useApiRequest';
  import PayClaimForm from '@/features/claim/components/form/PayClaimForm.vue'
  import { payClaim } from '@/features/claim/api/claimApi'
  import { toast, getFormData } from '@/util/utils';

  const props = defineProps({
    batchCode: {
      required: true,
      type: String,
    },
    providerUuid: {
      required: true,
      type: String,
    },
    amount: {
      required: true,
      type: Number,
    }
  })
  const payReq = useApiRequest()

  function submit(values) {
    const json = JSON.stringify({
      ...props,
      ...values,
      file: undefined
    })
    const fd = getFormData({
      file: values.file,
      payClaimRequest: json
    })
    payReq.send(
      () => payClaim(JSON.parse(json)),
      res => {
        toast(res.success, 'Successfully Paid.', 'Couldnt Pay. try Again.')
      }
    )
  }
</script>
<template>
  <FormParent class="bg-white" title="Pay Claim">
    <PayClaimForm
      :payment="props"
      :on-submit="submit"
      :btn-text="'Pay'" 
    />
  </FormParent>
</template>