<script setup>
  import FormLayout from '@/components/FormLayout.vue';
  import FormModal from '@/components/FormModal.vue';
  import FormParent from '@/components/FormParent.vue';
  import FormSubmitButton from '@/components/FormSubmitButton.vue';
  import { Input } from '@/components/form_elements';
  import { closeModal } from '@/modals';
import { formatCurrency } from '@/util/utils';

  function submitForm(values) {
    closeModal(values)
  }
  
</script>

<template>
<FormModal v-slot="{ modal }" name="PayPremium">
    {{ console.log(modal) }}
    <FormParent class="self-start min-h-[12rem] bg-white" title="Add Receipt Number">
      <FormLayout v-slot="{ submit }">
        <Input
          class=""
          name="receiptNumber"
          label="Receipt Number"
          placeholder="Receipt Number"
          validation="required"
        />
        <Input
          class=""
          name="requestedPremium"
          label="Requested Premium"
          placeholder="Requested Premium"
          validation="required"
          :disabled="true"
          :value="formatCurrency(modal.totalPremium)"
        />
        <Input
          :disabled="true"
          :value="formatCurrency(5)"
          name="revenueStamp"
          label="Revenue Stamp"
          placeholder="Revenue Stamp"
          validation="required"
        />
        <Input
          class=""
          name="withHoldingTax"
          label="WithHolding Tax"
          placeholder="WithHolding Tax"
          validation="required"
          :disabled="true"
          :value="formatCurrency((modal.totalPremium * 2 / 100))"
        />
        <Input
          class=""
          name="netPremium"
          label="Net Premium"
          placeholder="Net Premium"
          :validation="{
            required: true,
            exactly: ((modal.totalPremium + 5) - (modal.totalPremium * 2 / 100)).toFixed(2)
          }"
          :value="((modal.totalPremium + 5) - (modal.totalPremium * 2 / 100)).toFixed(2)"
        />
        <FormSubmitButton
          class="col-span-2"
          btn-text="Pay"
          @click.prevent="submit(({values}) => submitForm({
            ...values,
            withHoldingTax: modal.totalPremium * 2 / 100,
            requestedPremium: modal.totalPremium,
            netPremium: ((modal.totalPremium + 5) - (modal.totalPremium * 2 / 100)).toFixed(2),
            receiptDate: '2024-02-12',
            revenueStamp: 5
          }))"
        />
      </FormLayout>
    </FormParent>
  </FormModal>
</template>