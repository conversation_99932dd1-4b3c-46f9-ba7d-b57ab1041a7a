              ;<div class="."></div><script setup>
  import { useApiRequest } from '@/composables/useApiRequest'
  import { getAllPackages } from '@/features/quotation/api/packageApi'
  import { computed } from 'vue';

  const props = defineProps({
    init: {
      type: Function
    }
  })

  const packaegsReq = useApiRequest()

  packaegsReq.send(
    () => getAllPackages(),
    res => {
      if(res.success) {
        console.log(res)
        props.init && props.init(res.data?.[0]?.packageName, res.data?.[0]?.packageUuid)
      }
    }
  )

  const packageNames = computed(() => packaegsReq.response.value?.map(el => el.packageName)?.toSorted())

  const rate = computed(() => (name, coverage, size) => {
    console.log(size, coverage, name, 'size')
    const packages = packaegsReq.response.value?.find(el => el.packageName == name)    
    const limit = packages?.benefitRanges?.find(el => el.familySize == size && parseFloat(coverage) >= el.minLimit && parseFloat(coverage) <= el.maxLimit)
    return limit?.rate || 0
  })

  const minmax = computed(() => {
    return (name) => {
      const pack = packaegsReq.response.value?.find(el => el.packageName == name)    
      return {min: pack?.minLimit, max: pack?.maxLimit} || 0
    }
  })

  const packageUuid = computed(() => (name) => packaegsReq.response.value?.find(el => el.packageName == name)?.packageUuid)
</script>
<template>
  <slot
    :packages="packaegsReq.response.value"
    :packageNames="packageNames"
    :pending="packaegsReq.pending.value"
    :rate="rate"
    :packageUuid="packageUuid"
    :minmax="minmax"
  />  
</template>