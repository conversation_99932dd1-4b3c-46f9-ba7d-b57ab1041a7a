import { useToast } from "vue-toastification";
import numberToWords from 'number-to-words'

let id = 0;
function* getId() {
  while (true) {
    yield `generated_id_${++id}`;
  }
}

export const genId = getId();

export function toast(type, success, error) {
  const toast = useToast();
  if (type) {
    toast.success(success);
  } else {
    toast.error(error);
  }
}

export function getQueryFormObject(query) {
  return Object.keys(query)
  .reduce((querys, q, idx) => {
    console.log(`${encodeURIComponent(q)}=${encodeURIComponent(query[q])}`)
    querys += `${encodeURIComponent(q)}=${encodeURIComponent(query[q])}`;
    if (idx != Object.keys(query).length - 1) querys += `&`;
    return querys;
  }, "?");
}

export function formatCurrency(currencyValue) {
  if (currencyValue !== 0 && !currencyValue) return;

  // const currencyFormat = new Intl.NumberFormat("am-ET", {
  //   style: "currency",
  //   currency: "ETB",
  // }).format(parseFloat(currencyValue));
  return `ETB ${formatNumberWithDecimal(parseFloat(currencyValue))}`;
}

export function formatNumber(number) {
  const formatter = new Intl.NumberFormat("en-US");

  const formattedNumber = formatter.format(number);
  return formattedNumber;
}

export function formatNumberWithDecimal(number) {
  const formatter = new Intl.NumberFormat("en-US", {
    useGrouping: true,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const formattedNumber = formatter.format(number);
  return formattedNumber;
}

export async function getBgbase64(path) {
  return new Promise(async (res) => {
    const logoBlob = await (await fetch(path)).blob();
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64Image = reader.result;
      res(base64Image);
    };
    reader.readAsDataURL(logoBlob);
  });
}

export function secondDateFormat(d, hours = false) {
  if (!d) return;
  
  let options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  if(hours) {
    options = {
      ...options,
      hour: 'numeric',
      hour12: true, // Use 24-hour format
      minute: 'numeric'
    }
  }

  const date = new Date(d);
  const dateFormat = new Intl.DateTimeFormat("en-US", options).format(date);
  return dateFormat;
}

export function getFormData(obj = {}) {
  const fd = new FormData()
  Object.keys(obj)
  .forEach(el => {
    fd.append(el, obj[el])
  })
  return fd
}

export function addDate(numDays) {
  var currentDate = new Date();
  currentDate.setDate(currentDate.getDate() + numDays);
  return currentDate;
}

export function toWords(num) {
  return numberToWords.toWords(num)
}