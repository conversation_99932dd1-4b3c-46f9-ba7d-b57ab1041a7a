import axios from "axios";
import { responseHand<PERSON> } from "./ApiResponseHandler";
import { useUserStore } from "../../stores/userStore";
import { getActivePinia } from "pinia";

const backendApiUrl = import.meta.env.VITE_API_URI;

export default class ApiService {
  /**
   * Axios instance
   */
  api;

  /**
   * Initialize the Axios instance
   */
  _initApi(baseURL) {
    this.api = axios.create({
      baseURL,
      validateStatus: (status) => {
        return status >= 200 && status < 300;
      },
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Constructor for the API service
   */
  constructor(baseURL) {
    if (baseURL) this._initApi(baseURL);
    else this._initApi(backendApiUrl);
  }

  /**
   * GET request
   */
  async get(url, config = {}) {
    return await responseHandler(
      this.api({
        ...config,
        url,
        method: "get",
      })
    );
  }

  /**
   * POST request
   */
  async post(url, data, config = {}) {
    return await responseHandler(
      this.api({
        ...config,
        url,
        data,
        method: "post",
      })
    );
  }

  /**
   * PUT request
   */
  async put(url, data, config = {}) {
    return await responseHandler(
      this.api({
        ...config,
        url,
        data,
        method: "put",
      })
    );
  }

  /**
   * PATCH request
   */
  async patch(url, data, config = {}) {
    return await responseHandler(
      this.api({
        ...config,
        url,
        data,
        method: "patch",
      })
    );
  }

  /**
   * DELETE request
   */
  async delete(url, config = {}) {
    return await responseHandler(
      this.api({
        ...config,
        url,
        method: "delete",
      })
    );
  }

  /**
   * Add a multipart header
   */
  addMultipartHeader() {
    this.api.defaults.headers.common["Content-Type"] = "multipart/form-data";
    return this;
  }

  /**
   * Add an authentication header dynamically
   */
  addAuthenticationHeader() {
    const pinia = getActivePinia();
    if (!pinia) throw new Error("Pinia is not initialized. Ensure app.use(pinia) is called.");

    const userStore = useUserStore(pinia); // Dynamically fetch the user store
    const user = userStore.user; // Get the user

    if (user?.accessToken) {
      this.api.defaults.headers.common.Authorization = `Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************.bJGH8sgviU0YyNerLxZYbLm8WV8OKB9W5H5yWcz-Zbw`;
    } else {
      throw new Error("No access token available for authentication.");
    }

    return this;
  }
}
