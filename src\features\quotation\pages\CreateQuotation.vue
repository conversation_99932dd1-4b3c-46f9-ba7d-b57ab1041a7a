<script setup>
import FormParent from '@/components/FormParent.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { mdiBullhorn, mdiClose, mdiMinusBox, mdiMinusCircle, mdiMinusThick, mdiOfficeBuilding, mdiPlus } from '@mdi/js';
import InstitutionForm from '../components/form/InstitutionForm.vue';
import PackagesDataProvider from '../components/PackagesDataProvider.vue';
import FormLayout from '@/components/FormLayout.vue';
import { formatCurrency, genId, toast } from '@/util/utils'
import { useApiRequest } from '@/composables/useApiRequest';
import { computed, ref, watch } from 'vue';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { Input, Textarea } from '@/components/form_elements';
import Select from '@/components/form_elements/Select.vue';
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import { useRoute } from 'vue-router'
import { useInstitutions } from '@/features/quotation/store/activeInstitutionsStore'
import { createInstitution, getInstitutionsId } from '../api/institutionApi';
import { createQuotation } from '../api/quotationApi';

const institutionStore = useInstitutions()

const route = useRoute()
const institutionUuid = ref(route.params?.id)

const institutionReq = useApiRequest()

const institution = ref(institutionStore.institutions.find(el => el.institutionUuid == institutionUuid))

if (!institution.value && institutionUuid.value) {
  institutionReq.send(
    () => getInstitutionsId(institutionUuid.value),
    res => {
      if (res.success) {
        institution.value = res.data
        institutionUuid.value = res.data?.institutionUuid
      }
    }
  )
}

function submit(values) {
  console.log(values)
  institutionReq.send(
    () => createInstitution(values),
    res => {
      console.log(res)
      toast(res.success, 'Added Succssfully', 'Couldnt Create. Try Again.')
      if (res.success) {
        institution.value = res.data
        institutionUuid.value = res.data?.institutionUuid
      }
    }
  )
}

const allMemberTYpes = [
  {
    label: 'Member Only',
    value: 1,
  },
  {
    label: 'Member + 1',
    value: 2,
  },
  {
    label: 'Member + 2',
    value: 3
  },
  {
    label: 'Member + 3',
    value: 4,
  },
  {
    label: 'Member + 4',
    value: 5
  },
  {
    label: 'Member + 5 or more',
    value: 6
  }
]
const plan = [
  {
    label: 'Individual Plan',
    value: 'Individual_Plan'
  },
  {
    label: 'Family Plan',
    value: 'Family_Plan'
  },
  {
    label: 'Family Shared Plan',
    value: 'Family_Shared_Plan'
  },
  {
    label: 'Member Only',
    value: 'Member_Only'
  }
]

const showSelectedPackageAddBtn = ref(false)
const selectedPackage = ref('Standard Cover')

const quotations = ref({
  institutionUuid: '',
  description: '',
  quoatedServices: []
})

function addQuotation(packageName, id) {
  console.log(id)
  if (!packageName) return
  const count = quotations.value.quoatedServices.filter(el => el.package == packageName).reduce((sum, el) => sum += 1, 0)

  if (count == allMemberTYpes.length) return

  quotations.value.quoatedServices.push({
    id: genId.next().value,
    packageUuid: id,
    description: 1,
    planType: 'Family_Plan',
    coverage: '',
    numberOfInsured: 0,
    premium: '',
    rate: '',
    package: packageName,
  })

  showSelectedPackageAddBtn.value = false
}

const selectedMembers = computed(() => (name) => {
  const res = quotations.value.quoatedServices.filter(el => el.package === name)
  const selected = res.map(el => el.description)
  return selected
})

const formatedQuotations = computed(() => (
  quotations.value.quoatedServices.reduce((state, payload) => {
    if (state[payload.package]) {
      state[payload.package].push(payload)
    } else {
      state[payload.package] = [payload]
    }
    return state
  }, {})
))

const selectedQuote = computed(() => (id) => quotations.value.quoatedServices.find(el => el.id == id))

function removePackage(name) {
  quotations.value.quoatedServices = quotations.value.quoatedServices?.filter(el => el.package != name)
}

function remove(id) {
  quotations.value.quoatedServices = quotations.value.quoatedServices?.filter(el => el.id != id)
}

watch(quotations.value.quoatedServices, () => {
  console.log(quotations.value)
}, {
  deep: true
})

function submitQuotation(packages) {
  if (institutionReq.pending.value) return

  quotations.value.quoatedServices.forEach(service => {
    const pcks = packages.find(el => el.packageName == service.package)
    const limits = pcks?.benefitRanges(el => el.familySize == service.description && parseFloat(service.coverage) >= el.minLimit && parseFloat(service.coverage) <= el.maxLimit)
    service.rate = limits?.rate || 0
    service.premium = (service.coverage * res?.rate / 100) || 0
  })

  institutionReq.send(
    () => createQuotation({ ...quotations.value, institutionUuid: institutionUuid.value }),
    res => {
      console.log(res)
      toast(res.success, 'Created Successfully', 'Couldnt Create. Try Again.')
    }
  )
}
</script>
<template>
  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiBullhorn" title="Quotation" main>
    </SectionTitleLineWithButton>
    <FormParent v-if="!institutionUuid && !institutionReq.response.value" title="Add Institution"
      class="bg-white rounded-md w-full">
      <InstitutionForm :pending="institutionReq.pending.value" :on-submit="submit" btn-text="Add"></InstitutionForm>
    </FormParent>
    <PackagesDataProvider :init="addQuotation" v-else-if="institutionUuid || institutionReq.response.value"
      v-slot="{ packages, rate, packageNames, minmax, pending, packageUuid }">
      <FormParent :title="`New Quotation for ${institution?.institutionName || ''}`"
        class="relative mt-3 bg-white rounded-md w-full">
        <FormLayout v-slot="{ submit }" class="px-2">
          <Input v-model="quotations.description" name="description" label="Description" placeholder="Description"
            validation="required|max-150" />
          <div class="flex z-10 items-center gap-2 top-[3px] right-0 h-12 py-3 px-2 absolute">
            <p>Select A Package</p>
            <select :value="packageNames?.[0]" @change="showSelectedPackageAddBtn = true" v-model="selectedPackage"
              class="min-w-[10rem] h-[2.07rem] p-0 px-2 text-sm rounded-md border border-gray-300" value="Standard Cover">
              <option v-for="p in packageNames || []">{{ p }}</option>
            </Select>
            <button @click.stop.prevent="addQuotation(selectedPackage, packageUuid(selectedPackage))"
              v-if="showSelectedPackageAddBtn"
              class="border items-center gap-1 flex border-gray-300 px-2 py-1 rounded-md bg-white text-primary">
              <BaseIcon :path="mdiPlus" />
              Add
            </button>
          </div>
          <div class="flex border-y pb-2 col-span-2 flex-col gap-2" :key="name"
            v-for="name in Object.keys(formatedQuotations)">
            <div class="flex items-center border-b h-10  justify-between">
              <p class="text-lg py-1 font-medium">{{ name }}</p>
              <button @click.prevent="removePackage(name)"
                class="w-8 h-8 flex justify-center items-center rounded-full bg-gray-300">
                <BaseIcon :size="22" :path="mdiClose" />
              </button>
            </div>
            <div class="grid grid-7-cols">
              <!-- <div class="flex-1 grid grid-cols-4 col-span-5"> -->
              <p># Insured</p>
              <p>Description</p>
              <p>Plan Type</p>
              <p>Coverage</p>
              <!-- </div> -->
              <!-- <div class="flex-1 grid grid-cols-3 col-span-2"> -->
              <p class="px-2">Rate</p>
              <p class="px-2">Premium</p>
              <!-- </div> -->
            </div>
            <div :key="quotation.id" v-for="(quotation, idx) in formatedQuotations[name]"
              class="flex justify-center flex-col gap-2 col-span-2">
              <!-- <div class="gap-4 grid grid-7-cols"> -->
              <div class="flex-1 gap-2 grid grid-7-cols">
                <Input v-model="selectedQuote(quotation.id).numberOfInsured" validation="required|num"
                  placeholder="insured persons" />
                <Select :obj="true" v-model="selectedQuote(quotation.id).description" :options="allMemberTYpes"
                  validation="required" />
                <Select :obj="true" v-model="selectedQuote(quotation.id).planType" value="Family" :options="plan"
                  validation="required" />
                <Input v-model="selectedQuote(quotation.id).coverage" :validation="{
                  required: true,
                  num_minmax: [minmax(name)?.min, minmax(name)?.max]
                }" placeholder="Sum Coverage" />
                <p class="p-2">
                  {{ rate(name, selectedQuote(quotation.id).coverage, selectedQuote(quotation.id).description) }}
                </p>
                <p class="p-2">
                  {{ formatCurrency(selectedQuote(quotation.id).numberOfInsured * (selectedQuote(quotation.id).coverage *
                    rate(name, selectedQuote(quotation.id).coverage, selectedQuote(quotation.id).description) / 100)) }}
                </p>
                <button @click.prevent="remove(quotation.id)"
                  class="w-6 h-6 flex justify-center items-center rounded-full bg-gray-300">
                  <BaseIcon class="text-red-500" :size="18" :path="mdiMinusThick" />
                </button>
              </div>
            </div>
            <div class="grid grid-7-cols gap-2 col-span-2">
              <p class="col-span-5"></p>
              <div class="flex gap-2 col-span-2">
                <p class="font-semibold">total</p>
                <p>
                  {{
                    formatCurrency(quotations.quoatedServices?.filter(el => el.package == name)
                      ?.reduce((sum, el) => {
                        return sum += (el.numberOfInsured * el.coverage * rate(name, el.coverage, el.description) / 100)
                      }, 0))
                  }}
                </p>
              </div>
            </div>
            <div class="col-span-2 flex pt-2 border-t justify-end">
              <button @click.prevent="addQuotation(name, packageUuid(name))"
                class="border items-center gap-1 flex border-gray-300 px-2 py-1 rounded-md bg-white text-primary">
                <BaseIcon :size="20" :path="mdiPlus" />
                Add
              </button>
            </div>
          </div>
          <FormSubmitButton :pending="institutionReq.pending.value"
            @click.prevent="submit(() => submitQuotation(packages))" class="col-span-2" btn-text="Create Quotation" />
        </FormLayout>
      </FormParent>
    </PackagesDataProvider>
  </SectionMain>
</template>
<style></style>