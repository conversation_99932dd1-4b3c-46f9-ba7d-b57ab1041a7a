<script setup>
import { ref, onMounted, watch, computed } from "vue";
import * as session from "@/scripts/session";
import { useRouter, useRoute } from "vue-router";
import LayoutAuthenticated from "@/layouts/LayoutAuthenticated.vue";
import SectionMain from "@/components/section/SectionMain.vue";
import SectionTitleLineWithButton from "@/components/section/SectionTitleLineWithButton.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import EditMember from "@/components/forms/EditMember.vue";
import AddMember from "@/components/forms/AddMember.vue";
import {
  mdiAccountStar,
  mdiPlus,
  mdiTrayArrowUp,
  mdiChevronDoubleRight,
  mdiChevronDoubleLeft,
} from "@mdi/js";
import Modal from "@/components/modal.vue";
import ContractDetailCard from "@/components/contractDetailCard.vue";
import AddInstitutionContract from "@/components/forms/addInstitutionContract.vue";
import api from "@/scripts/api";
import loader from "@/components/loader/loader.vue";
import { useToast } from "vue-toastification";
import { Icon } from "@iconify/vue";
import AddBeneficiary from "@/components/forms/AddDependent.vue";

const toast = useToast();

const totalPages = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(25);
const pageSizes = ref([25, 50, 75, 100, 125, 150]);
const loading = ref(false);
const route = useRoute();
const users = ref([]);
const firstName = ref("");
const phone = ref("");
const address = ref("");
const showName = ref(false);
const showPhone = ref(false);
const showAddress = ref(false);
const openImportModal = ref(false);
const openAddModal = ref(false);
const openEditModal = ref(false);
const openBeneModal = ref(false);
const importedFile = ref();
const member = ref([]);
const isLoading = ref(false);
const myUser = ref([]);
const institutionName = ref('');
const search = ref(sessionStorage.getItem("searchTerm") || "");

watch(search, (newSearchTerm) => {
  sessionStorage.setItem("searchTerm", newSearchTerm);
  if (newSearchTerm === "") {
    fetchAllUsers(currentPage.value, itemsPerPage.value);
  }
});

const toggleName = () => {
  showName.value = !showName.value;
};

const togglePhone = () => {
  showPhone.value = !showPhone.value;
};

const toggleAddress = () => {
  showAddress = !showAddress;
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }

  fetchAllUsers(currentPage.value, itemsPerPage.value);
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
  fetchAllUsers(currentPage.value, itemsPerPage.value);
};

const handleFileChange = async (event) => {
  importedFile.value = event.target.files[0];
};

const submitInsuredPersonAttachment = async (event) => {
  event.preventDefault();
  openImportModal.value = !openImportModal.value;
  isLoading.value = true;
  let formData = new FormData();
  formData.append("file", importedFile.value);
  try {
    await api.makeAuthenticatedRequest({
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `/insuredperson/import-insured-and-dependant?institutionUuid=${route.params.Uuid}&payerInstitutionContractUuid=${route.params.id}`,
      data: formData,
    }).then((data) => {
      isLoading.value = false;
      toast.success(data.message);
      if (data) {
        fetchAllUsers(currentPage.value, itemsPerPage.value);
      }
    });
  } catch (error) {
    isLoading.value = false;
    toast.error(error.message);
  }
};

const fetchAllUsers = async (currentPage, myItemsPerPage) => {
  loading.value = true;
  await api.get(`/insuredperson/list/${route.params.id}?page=${currentPage}&limit=${myItemsPerPage}`).then((data) => {
    users.value = data;
    loading.value = false;
    if (data.length != 0) {
      totalPages.value = data[0].totalPages;
    }
  });
};

const openEditMyModal = (user) => {
  openEditModal.value = !openEditModal.value
  myUser.value = user
}

const openBeneficiaryModal = (user) => {
  openBeneModal.value = !openBeneModal.value
  member.value = user
}

const closeBeneModal = async () => {
  openBeneModal.value = !openBeneModal.value
}

const openMyAddModal = () => {
  openAddModal.value = !openAddModal.value
}

const handleItemsPerPageChange = () => {
  const itemsPage = itemsPerPage.value;
  fetchAllUsers(currentPage.value, itemsPage);
};

const getInstitution = async () => {
  isLoading.value = true;
  await api.get(`/institution/${route.params.Uuid}`).then((data) => {
    institutionName.value = data.institutionName;
    isLoading.value = false;
  });
};

const filteredItems = computed(() => {
  if (!firstName.value && !address.value && !phone.value) {
    return users.value;
  } else {
    return users.value.filter(
      (item) =>
        item.firstName.toLowerCase().includes(firstName.value.toLowerCase()) &&
        item.Address.toLowerCase().includes(address.value.toLowerCase()) &&
        item.phone.toLowerCase().includes(phone.value.toLowerCase())
    );
  }
});

onMounted(() => {
  handleItemsPerPageChange();
  getInstitution();
});
</script>

<template>
  <div class="mt-5">
    <Modal :open="openAddModal" @close="openAddModal = false" title="Add Member ">
      <AddMember></AddMember>
    </Modal>
    <Modal :open="openEditModal" @close="openEditModal = false" title="Edit Member ">
      <EditMember :user=myUser></EditMember>
    </Modal>
    <Modal :open="openBeneModal" @close="openBeneModal = !openBeneModal" title="Dependent Registration">
      <AddBeneficiary @close="closeBeneModal" :member=member></AddBeneficiary>
    </Modal>
    <Modal :open="openImportModal" @close="openImportModal = !openImportModal" title="Import Insured Members">
      <form @submit="submitInsuredPersonAttachment" enctype="multipart/form-data" class="mt-10 flex flex-col gap-y-10">
        <div
          class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:w-[383px]">
          <input type="file" name="attachment" id="attachment"
            class="px-4 block flex-1 border-0 bg-transparent py-1.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
            placeholder="attachment" @change="handleFileChange($event)" />
        </div>
        <div class="flex w-full justify-center">
          <button
            class="bg-primary hover:bg-primary hover:shadow-lg duration-200 px-10 py-1 text-lg text-white font-semibold rounded-lg">
            Import
          </button>
        </div>
      </form>
    </Modal>
    <SectionMain class="">
      <SectionTitleLineWithButton :icon="mdiAccountStar" :title="institutionName + ' ' + 'Policy Holders'" main>

        <div class="flex space-x-3">
          <form class="w-full mb-5">
            <label for="default-search"
              class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
            <div class="relative">
              <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                <Icon icon="mdi:magnify" class="text-2xl" />
              </div>
              <input type="search" id="default-search" v-model="search"
                class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search . . ." />
              <button type="submit" @click="fetchAllUsers"
                class="text-white absolute end-2.5 bottom-2.5 bg-primary0 hover:bg-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg duration-200 text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Search
              </button>
            </div>
          </form>
          <div class="flex space-x-2">
            <div class="mt-2">
              <BaseButton :icon="mdiPlus" color="whiteDark" @click="openMyAddModal" label="Add Member" />
            </div>
            <div class="mt-2">
              <BaseButton :icon="mdiTrayArrowUp" color="whiteDark" @click="openImportModal = true"
                label="Import Members" />
            </div>
          </div>
        </div>

      </SectionTitleLineWithButton>

      <div class="min-h-[70vh]">
        <div class="flow-root" v-if="!loading">
          <div class="overflow-x-auto">
            <div class="ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table class="min-w-full ring-1 ring-black ring-opacity-5 sm:rounded-lg xl:max-h-[20rem] overflow-y-auto">
                <thead class="sticky top-0 rounded-t-lg">
                  <tr class="px-3 divide-gray-200 bg-gray-200">
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"></th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"
                      @click="toggleName">
                      <div>
                        <input v-if="showName" type="text" v-model="firstName" placeholder="Name"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>Full Name</p>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      <span>Gender</span>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"
                      @click="togglePhone">
                      <div>
                        <input v-if="showPhone" type="text" v-model="phone" placeholder="phone"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>phone</p>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Policy Holder No
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"
                      @click="toggleAddress">
                      <div>
                        <input v-if="showAddress" type="text" v-model="address" placeholder="Address"
                          class="h-4 box-content rounded-md w-25" />
                        <p v-else>Address</p>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Effective Date
                    </th>
                    <!-- <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                        Premium
                      </th> -->
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                      Status
                    </th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"></th>
                    <th scope="col" class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap"></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-for="(user, index) in filteredItems" :key="user.item"
                    class="divide-gray-200 px-3 hover:bg-primary duration-200 items-center even:bg-gray-50">
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-center font-medium text-gray-900">
                      {{ index + 1 }}
                    </td>
                    <td class="whitespace-wrap text-sm py-2 px-2 text-gray-500 group relative">
                      <p class="w-[15rem] whitespace-pre-wrap">
                        {{ user?.title }} {{ user?.firstName }}
                        {{ user?.fatherName }} {{ user?.grandFatherName }}
                      </p>
                    </td>
                    <td class="whitespace-wrap text-sm py-2 px-2 text-gray-500">
                      {{ user?.gender }}
                    </td>
                    <td class="whitespace-wrap text-sm py-2 px-2 text-gray-500">
                      {{ user?.phone }}
                    </td>
                    <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                      {{ user?.insuranceId }}
                    </td>
                    <td class="whitespace-wrap text-sm py-2 px-2 text-gray-600">
                      {{ user?.state }}, {{ user?.country }}
                    </td>
                    <td class="whitespace-wrap text-sm py-2 px-2 text-gray-600">
                      {{ user?.beginDate }} &nbsp; to &nbsp;
                      {{ user?.endDate }}
                    </td>
                    <!-- <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-600">
                        {{ user?.premium }}
                      </td> -->
                    <td class="whitespace-nowrap text-sm py-2 px-2 font-medium">
                      <div class="flex w-full">
                        <span class="px-3 py-0.5 rounded-full text-center" :class="user?.status == 'ACTIVE'
                          ? 'bg-green-100 text-green-600'
                          : 'bg-red-100 text-red-600'
                          ">
                          {{ user?.status }}
                        </span>
                      </div>
                    </td>
                    <td class="whitespace-nowrap py-2 px-2 text-primary font-semibold underline underline-offset-4">
                      <button title="Edit" class="bg-white p-1 rounded-lg hover:shadow-xl duration-200">
                        <Icon icon="tabler:edit" @click="openEditMyModal(user)" class="text-primary text-2xl" />
                      </button>
                      <button title="Open Dependents" class="bg-white p-1 m-1 rounded-lg hover:shadow-xl duration-200"
                        @click="openBeneficiaryModal(user)">
                        <div class="flex flex-row">
                          <Icon icon="tabler:users-group" class="text-primary text-2xl" />
                          <!-- <span>Dependents</span> -->
                        </div>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <p v-if="filteredItems && filteredItems.length === 0"
                class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">
                No Users Were Found
              </p>
            </div>
          </div>
        </div>
        <div v-else class="flex items-center justify-center h-[40vh]">
          <loader />
        </div>
      </div>
      <div class="w-full flex justify-end mt-2">
        <div class="flex items-center space-x-2">
          <div class="bg-white">
            <BaseButton :icon="mdiChevronDoubleLeft" label="Previous" title="previous page" color="whiteDark"
              @click="previousPage" :disabled="currentPage === 1" />
          </div>
          <div>
            <div>
              Items per page:
              <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                <option v-for="size in pageSizes" :key="size" :value="size">
                  {{ size }}
                </option>
              </select>
            </div>
          </div>
          <div>
            <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page" color="whiteDark" @click="nextPage"
              :disabled="currentPage === totalPages" />
          </div>
        </div>
      </div>
    </SectionMain>
  </div>
</template>
