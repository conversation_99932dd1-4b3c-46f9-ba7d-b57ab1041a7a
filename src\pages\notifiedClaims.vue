<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import { mdiBadgeAccount, mdiChevronDoubleLeft, mdiChevronDoubleRight } from '@mdi/js';
import * as session from '@/scripts/session';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import loader from '@/components/loader/loader.vue';
import { computed, ref, watch, watchEffect, onMounted } from 'vue';
import BaseButton from '@/components/base/BaseButton.vue';
import api from '@/scripts/api';
import NoData from '@/assets/img/no-data-here.png';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';
import customSelect from '@/components/forms/custom/select.vue';


const router = useRouter();
const user = session.getUser();
const toast = useToast();
const emit = defineEmits(['delete']);

const totalPages = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(25);
const pageSizes = ref([25, 50, 75, 100, 125, 150]);
const claims = ref([]);
const providers = ref([]);
const institutions = ref([]);
const loading = ref(false);
const claimTypes = ref('');
const roleType = ref('');
const orderBy = ref('asc');
const openInvoiceModal = ref(false);
const selectedClaims = ref([]);
const providerUuid = ref(null);
const payerUuid = ref('');

onMounted(async () => {
  fetchClaims(providerUuid.value, null);
  fetchProviders();
});

watch(providerUuid, async (newUuid, oldUuid) => {
  console.log('providerUuid changed:', newUuid, oldUuid);
  if (newUuid !== oldUuid) {
    fetchInstitutions(newUuid)
    fetchClaims(newUuid, null)
  }
});

watch(payerUuid, async (newUuid, oldUuid) => {
  if (newUuid !== oldUuid) {
    fetchClaims(providerUuid.value, newUuid)
  }
});

const handleModalState = (claimUuid) => {
  router.push({ name: 'claim-detail', params: { id: claimUuid } });
};

const previousPage = () => {

}

const nextPage = () => {

}

const fetchClaims = async (Pid, IUuid) => {
  loading.value = true;
  await api.get(`/claim/requested/list?providerUuid=${Pid}&institutionUuid=${IUuid}&page=1&limit=25`).then((data) => {
    claims.value = data;
    claimTypes.value = 'Credit';
    roleType.value = 'Reviewer';
    loading.value = false;
  });
};

const fetchProviders = async () => {
  loading.value = true;
  await api.get(`/provider/list/name?page=1&limit=25`).then((data) => {
    providers.value = data;
    loading.value = false;
  });
}

const fetchInstitutions = async (id) => {
  loading.value = true;
  try {
    await api.get(`/payer-institution-contract/list/institutions/${id}`).then((data) => {
      institutions.value = data;
      loading.value = false;
    });
  } catch (error) {
    loading.value = !loading.value
    toast.error(error.message)
  }
}

const handleSort = (column) => {
  if (orderBy.value === 'asc') {
    claims.value.sort((a, b) => {
      if (a[column] && b[column]) {
        return a[column].localeCompare(b[column]);
      } else {
        return 0;
      }
    });
    orderBy.value = 'desc';
  } else {
    claims.value.sort((a, b) => {
      if (a[column] && b[column]) {
        return b[column].localeCompare(a[column]);
      } else {
        return 0;
      }
    });
    orderBy.value = 'asc';
  }
};

const filteredClaims = ref(claims);

const selectAllClaims = (event) => {
  if (event.target.checked) {
    selectedClaims.value = filteredClaims.value.map((claim) => claim.claimUuid);
    console.log(selectedClaims.value);
  } else {
    selectedClaims.value = [];
    console.log(selectedClaims.value);
  }
};

const fullInfoOfSelectedClaims = computed(() => {
  return filteredClaims.value.filter((claim) =>
    selectedClaims.value.includes(claim.claimUuid)
  );
});

watchEffect(() => {
  console.log('fullInfoOfSelectedClaims', fullInfoOfSelectedClaims.value);
});

const fetchClaimLog = async (claimUuid) => {
  await api.get(`/claim/logs?claimUuid=${claimUuid}`).then((data) => {
    console.log('log for the claim is ', data);
  });
};
</script>
<template>
  <SectionMain>
    <SectionTitleLineWithButton :icon="mdiBadgeAccount" :title="claimTypes + ' ' + 'Claims'" main>
      <div class="flex">
        <div class="sm:col-span-2 m-1">
          <customSelect label="Provider Name" placeholder="All" :items="providers" valueKey="providerUuid"
            displayKey="providerName" v-model="providerUuid" />
        </div>
        <div class="sm:col-span-2 m-1">
          <customSelect label="Institution Name" placeholder="All" :items="institutions" value-key="institutionUuid"
            display-key="institutionName" v-model="payerUuid" />
        </div>
      </div>
    </SectionTitleLineWithButton>
    <div v-if="loading" class="flex items-center justify-center h-[40vh]">
      <loader />
    </div>
    <div class="h-full  bg-gray-50" v-else>
      <div class="min-h-[70vh] flex items-center justify-center flex-col gap-5"
        v-if="filteredClaims && filteredClaims.length === 0 && !loading">
        <img :src="NoData" alt="" />
        <div class="text-sm text-primary font-semibold">
          No {{ claimTypes }} claims found
        </div>
      </div>
      <div class="" v-if="claims && claims.length">
        <div class="overflow-x-auto">
          <div class="inline-block min-w-full py-2 align-middle">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="border-t-2 border-solid border-black">
                <tr
                  class="h-2 box-content divide-gray-200 px-3 hover:bg-primary duration-200 items-center even:bg-gray-50">
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    No.
                  </th>

                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Policy Holder Name
                  </th>

                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Provider Name
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Provider Phone
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Group Name
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Total Amount
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                    Status
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer"
                    @click="handleSort('claimDate')">
                    Claim Date
                    <span v-if="orderBy === 'asc'">▲</span>
                    <span v-if="orderBy === 'desc'">▼</span>
                  </th>
                  <th scope="col"
                    class="px-3 border-b-2 border-solid py-3.5 text-left text-base font-semibold text-gray-900">
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <tr v-for="(claim, index) in filteredClaims" :key="claim.claimUuid" :class="[
                  ' cursor-pointer hover:bg-gray-100 even:bg-gray-100',
                  selectedClaims.includes(claim.claimUuid)
                    ? ' bg-primary0 hover:bg-primary0'
                    : 'text-gray-900 ',
                ]" @click="handleModalState(claim.claimUuid, $event)">
                  <td class="whitespace-nowrap px-3 py-2  border-gray-600 text-base text-gray-500">
                    {{ index + 1 }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2  border-gray-600 text-base text-gray-500"
                    v-if="claim.dependantUuid == null">
                    {{ claim?.title }}
                    {{
                      claim.firstName +
                      ' ' +
                      claim.fatherName +
                      ' ' +
                      claim.grandFatherName
                    }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2  border-gray-600 text-base text-gray-500" v-else>
                    {{
                      claim.dependantFirstName +
                      ' ' +
                      claim.dependantFatherName +
                      ' ' +
                      claim.dependantGrandFatherName
                    }}
                  </td>

                  <td class="whitespace-nowrap px-3 py-2  border-gray-600 text-base text-gray-500">
                    {{ claim.providerName }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2  border-gray-600 text-base text-gray-500">
                    {{ claim.providerPhone }}
                  </td>

                  <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                    {{ claim.institutionName }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                    {{ claim.totalAmount }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                    Pending
                  </td>
                  <td class="whitespace-nowrap px-3 py-2 border-gray-600 text-base text-gray-500">
                    {{ claim.claimDate }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2 border-b border-gray-600 text-base text-gray-500">
                    <div class="bg-primary px-2 py-1  text-white text-center cursor-pointer rounded-md">
                      <div class="text-base capitalize">view</div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="w-full flex justify-end mt-2">
              <div class="flex items-center space-x-2">
                <div class="bg-white">
                  <BaseButton :icon="mdiChevronDoubleLeft" label="Previous" title="previous page" color="whiteDark"
                    @click="previousPage" :disabled="currentPage === 1" />
                </div>
                <div>
                  <div>
                    Items per page:
                    <select v-model="itemsPerPage" @change="handleItemsPerPageChange">
                      <option v-for="size in pageSizes" :key="size" :value="size">
                        {{ size }}
                      </option>
                    </select>
                  </div>
                </div>
                <div>
                  <BaseButton :icon="mdiChevronDoubleRight" label="Next" title="next page" color="whiteDark"
                    @click="nextPage" :disabled="currentPage === totalPages" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </SectionMain>
</template>
