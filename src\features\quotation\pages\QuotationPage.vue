<script setup>
import TableWrapper from '@/components/TableWrapper.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { ref } from 'vue';

const props = defineProps({
  icon: {
    required: true,
    type: String
  },
  to: String,
  btnLabel: {
    type: String
  },
  DataProvider: {
    type: Object
  },
  store: {
    type: Object
  },
  status: {
    type: String
  },
  headers: Object
})

const search = ref()
</script>
<template>
  <div class="w-full h-max">
    <SectionMain class="flex flex-col">
      <SectionTitleLineWithButton :to="to" :btn-label="btnLabel" :title="''" main>
        <div class="p-2">
          <input v-model="search" placeholder="Search ..." class="rounded-md border-gray-200" />
        </div>
      </SectionTitleLineWithButton>
      <slot :search="search" />
      <slot :search="search" name="content">
        <component :store="store" :search="search" :status="status" :is='DataProvider'
          v-slot="{ institutions, pending, error }">
          <TableWrapper :pending="pending" :Fallback="TableRowSkeleton" :defaultAction="false" :headers="{
        head: ['Institution', 'Phone', 'description',].concat(headers?.head, ...['modify']),
        row: ['institutionName', 'institutionPhone', 'description'].concat(headers?.row || [])
      }" :rows="institutions">
            <template #actions="{ row }">
              <slot name="actions" :row="row" />
            </template>
            <template #additionalBtns="{ row }">
              <slot :row="row" name="additionalBtns" />
            </template>
          </TableWrapper>
        </component>
      </slot>
    </SectionMain>
    <slot name="extra"></slot>
  </div>
</template>