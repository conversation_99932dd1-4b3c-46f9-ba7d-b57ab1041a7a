import api from "@/scripts/api";
import { handleError } from "vue";
import { useToast } from "vue-toastification";
const toast = useToast();
export default {
  async getPackageRatePerperson() {
    try {
      const response = await api.get("/package");
      return response;
    } catch (error) {
      return handleError(error);
    }
  },

  async addPackageRatePerperson(data) {
    let pdata = {};
    try {
      await api.post("/individualbenefitrange", data).then((response) => {
        toast.success("package rate for individual created successefuly");
        pdata = response;
      });

      return pdata;
    } catch (error) {
      toast.error("Oops some thing eror", error);
      return handleError(error);
    }
  },

  async updatePackage(data, id) {
    console.log("Reached at update serviece", data, id);
    let rdata = [];
    try {
      const response = await api
        .put(`/individualbenefitrange`, data)
        .then((response) => {
          rdata = response;
          toast.success("per person rate updated successfully");
        });

      return rdata;
    } catch (error) {
      toast.error("Oops! some thing error");
      return handleError(error);
    }
  },

  // update package status
  async updatePackageStatus(data, id) {
    let rdata = [];
    try {
      const response = await api
        .put(`/individualbenefitrange`, data)
        .then((response) => {
          rdata = response;
          toast.success("per person rate updated successfully");
        });

      return rdata;
    } catch (error) {
      toast.error("Oops! some thing error");
      return handleError(error);
    }
  },

  async getPackageById(id) {
    try {
      let response = await api.get(
        `/individualbenefitrange/active/benefit-ranges-per-package/${id}`
      );

      return response;
    } catch (error) {
      return handleError(error);
    }
  },

  async deletePackagePerpersonById(id) {
    try {
      const response = await api.delete(`/individualbenefitrange/${id}`);
      toast.success("Coverage deleted successfully");
      return true;
    } catch (error) {
      toast.error(error);
    }
  },
};
