<script setup>
import { mdiChat } from '@mdi/js';
import BaseIcon from './components/base/BaseIcon.vue';
import { onMounted, ref } from 'vue';
import ChatBox from './components/ChatBox.vue';

const open = ref(false)

</script>
<template>
  <div :class="[open ? 'w-[22rem] h-[35rem] bg-white' : 'bg bg-primary w-12 h-12']" class="rounded-md z-50 transition-all duration-200 ease-linear cursor-pointer shadow-lg  text-white flex justify-center items-center fixed right-4 bottom-4 overflow-hidden">
    <ChatBox v-if="open" />
    <button v-if="!open" class="grid place-items-center transition-all duration-200 ease-linear" :class="[open ? 'translate-y-[30rem]' : 'translate-y-0']">
      <base-icon :size="24" :path="mdiChat" />
    </button>
  </div>
  <router-view></router-view>
</template>

<script>
export default {
  name: "App"
};
</script>

<style>
  .chat-grid {
    display: grid;
    grid-auto-flow: row;
    grid-template-columns: 1fr;
    grid-template-rows: 3rem 1fr auto;
  }

  .grid-col-reverse {
    grid-auto-flow: column-reverse
  }
</style>