<script setup>
import * as session from '@/scripts/session';
import { useRouter } from 'vue-router';
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue';
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import { mdiAccountSettingsOutline, mdiPlus } from '@mdi/js';
import Modal from '@/components/modal.vue';
import rolesList from '@/components/tables/roles-list.vue';
import SvgIcon from '@jamescoyle/vue-icon';

const router = useRouter();

const logout = () => {
  session.setUserIsLoggedIn(false);
  router.push('/login');
};
</script>
<template>
  <div>
    <Modal :open="open">
      <RegistrationForm></RegistrationForm>
    </Modal>
      <SectionMain>
        <SectionTitleLineWithButton :icon="mdiAccountSettingsOutline" title="Roles" main>
          <div class="bg-primary round-xl flex gap-0.5 cursor-pointer item-center text-white px-3 py-2 rounded-lg"
            @click="openModal">
            <div class="text-white">
              <svg-icon type="mdi" :path="mdiPlus"></svg-icon>
            </div>
            <div class="font-medium text-base">Add a Providers</div>
          </div>
        </SectionTitleLineWithButton>
        <!-- Section starts -->
        <div class="">
          <div class="w-3/4">
            <roles-list></roles-list>
          </div>
        </div>
      </SectionMain>
  </div>
</template>
