<script setup>
import SectionMain from '@/components/section/SectionMain.vue';
import SectionTitleLineWithButton from '@/components/section/SectionTitleLineWithButton.vue';
import api from '@/scripts/api';
import { mdiHospitalBuilding, mdiPlus } from '@mdi/js';
import customInput from '@/components/forms/custom/input.vue';
import ConfirmModal from '@/components/ConfirmModal.vue';
import { Icon } from "@iconify/vue";
import loader from '@/components/loader/loader.vue';
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from 'vue-toastification';
import { Field } from 'vee-validate';

const router = useRouter()
const route = useRoute()
const toast = useToast()

const status = ref('ACTIVE');
const description = ref('');
const endDate = localStorage.getItem('endDate') ? ref(localStorage.getItem('endDate')) : ref('');
const beginDate = localStorage.getItem('begDate') ? ref(localStorage.getItem('begDate')) : ref('');
const contractName = ref('');
const contractCode = ref('');
const benefit = ref('');
const premium = ref('');
const institutionInsuranceNumber = localStorage.getItem('INumber') ? ref(localStorage.getItem('INumber')) : ref('');
const CoreInstitution11 = ref(null);
const contractNameVal = ref(null);
const contractCodeVal = ref(null);
const benefitVal = ref(null);
const premiumVal = ref(null);
const beginDateVal = ref(null);
const endDateVal = ref(null);
const contractData = ref([]);
const contracts = ref([]);
const isLoading = ref(false);
const approveModal = ref(false);

const fetchAllContracts = async () => {
    isLoading.value = true;
    await api.get(`/payer-institution-contract/lists/${route.params.Uuid}?status=ACTIVE`).then((data) => {
        contracts.value = data;
        isLoading.value = false;
    });
};

watch(institutionInsuranceNumber, (newVal) => {
    localStorage.setItem('INumber', newVal);
});

watch(beginDate, (newVal) => {
    localStorage.setItem('begDate', newVal);
});

watch(endDate, (newVal) => {
    localStorage.setItem('endDate', newVal);
});

const goBack = () => {
    router.back();
}

const addCategory = async () => {
    contractData.value = {
        institutionUuid: route.params.Uuid,
        contractName: contractName.value,
        contractCode: contractCode.value,
        benefit: benefit.value,
        premium: premium.value,
        beginDate: beginDate.value,
        endDate: endDate.value,
        status: 'ACTIVE',
    };
    contractNameVal.value.validateInput();
    contractCodeVal.value.validateInput();
    benefitVal.value.validateInput();
    premiumVal.value.validateInput();
    beginDateVal.value.validateInput();
    endDateVal.value.validateInput();
    if (!contractNameVal.value.hasError && !contractCodeVal.value.hasError && !benefitVal.value.hasError && !premiumVal.value.hasError && !beginDateVal.value.hasError && !endDateVal.value.hasError) {
        approveModal.value = true;
    }
};

async function confirmApprove() {
    isLoading.value = true;
    try {
        await api.post(`/payer-institution-contract`, contractData.value).then((data) => {
            isLoading.value = false;
            approveModal.value = false;
            toast.success(data.message);
            fetchAllContracts();
        });
    } catch (error) {
        toast.error(error.message);
    }
}

const SaveContract = async () => {
    isLoading.value = true;
    try {
        await api.put(`/institution/set-institution-insurance-number/${route.params.Uuid}?institutionInsuranceNumber=${institutionInsuranceNumber.value}`, contractData.value).then((data) => {
            isLoading.value = false;
            approveModal.value = false;
            toast.success(data.message);
            localStorage.removeItem('INumber');
            localStorage.removeItem('begDate');
            localStorage.removeItem('endDate');
        });
    } catch (error) {
        toast.error(error.message);
    }
}

onMounted(() => {
    fetchAllContracts()
})
</script>
<template>
    <div class="w-full h-max">
        <ConfirmModal v-model="approveModal" @confirm="confirmApprove" icon="simple-line-icons:check"
            title="Create New Policy" description="Are you sure you want to create this Policy?" confirmButton="create"
            iconClass="text-primary p-1 text-3xl" iconWrapperClass="bg-primary rounded-full p-1"
            confirmButtonClass="inline-flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary sm:ml-3 sm:w-auto duration-300" />
        <SectionMain>
            <SectionTitleLineWithButton :icon="mdiHospitalBuilding" title="Add Policy Holders" main>
            </SectionTitleLineWithButton>
            <div class="w-full mx-auto bg-white rounded-xl shadow-md overflow-hidden p-8 m-3">
                <div class="">
                    <div class="sm:col-span-6 text-lg font-semibold bg-gray-100 px-2">
                        Policy Particulars
                    </div>
                    <div class="flex flex-wrap">
                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="text" placeholder="" ref="CoreInstitution11" label="Group Policy Number"
                                v-model="institutionInsuranceNumber" name="groupPolicyNumber" :required="true" />
                        </div>
                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="date" placeholder="" ref="beginDateVal" label="Effective Date"
                                v-model="beginDate" name="beginDate" :required="true" />
                        </div>
                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="date" placeholder="" ref="endDateVal" label="End Date" v-model="endDate"
                                name="endDate" :required="true" />
                        </div>
                    </div>
                    <div class="sm:col-span-3 m-2">
                        <label for="status" class="block font-medium leading-6 text-gray-900">Policy Status</label>
                        <div class="mt-2">
                            <Field as="select" name="status" v-model="status"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:leading-6">
                                <option value="ACTIVE">Active</option>
                                <option value="PENDING">Pending</option>
                            </Field>
                        </div>
                    </div>
                    <div class="sm:col-span-6 text-lg font-semibold bg-gray-100 px-2">
                        Particulars of Benefit
                    </div>

                    <div class="flex flex-nowrap">
                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="text" placeholder="" ref="contractNameVal" label="Category Name"
                                v-model="contractName" :required="true" />
                        </div>

                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="text" placeholder="" ref="contractCodeVal" label="Category Code"
                                v-model="contractCode" :required="true" />
                        </div>

                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="number" placeholder="" ref="benefitVal" label="Benefit" v-model="benefit"
                                :required="true" />
                        </div>

                        <div class="flex flex-col space-y-1 m-2 flex-grow">
                            <customInput type="number" placeholder="" ref="premiumVal" label="Premium" v-model="premium"
                                :required="true" />
                        </div>

                        <div class="flex flex-col space-y-1 m-2 flex-grow mt-10">
                            <button
                                class="flex items-center text-lg px-4 py-1 font-semibold text-white bg-primary0 rounded-lg hover:shadow-lg duration-200 hover:bg-primary"
                                @click="addCategory">
                                <Icon icon="lucide:check" class="text-2xl" />
                                Save
                            </button>
                        </div>
                    </div>

                    <table
                        class="min-w-full overflow-hidden ring-1 ring-black ring-opacity-5 sm:rounded-lg  xl:max-h-[20rem] overflow-y-auto">
                        <thead class="sticky top-0 rounded-t-lg">
                            <tr class="px-3 divide-gray-200 bg-gray-200">
                                <th scope="col"
                                    class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                                </th>
                                <th scope="col"
                                    class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                                    Membership Category Code
                                </th>
                                <th scope="col"
                                    class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                                    <span>Membership Description</span>
                                </th>
                                <th scope="col"
                                    class="px-2 py-2 text-center font-semibold text-gray-900 whitespace-nowrap">
                                    Status
                                </th>
                                <th scope="col"
                                    class="px-2 py-2 text-left font-semibold text-gray-900 whitespace-nowrap">
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white" v-if="!isLoading">
                            <tr v-for="(service, index) in contracts" :key="service.item"
                                class="divide-gray-200 px-3 hover:bg-primary duration-200 items-center even:bg-gray-50">
                                <td class="whitespace-nowrap text-sm py-2 px-2 text-center font-medium text-gray-900">
                                    <span class="ml-3">
                                        {{ index + 1 }}
                                    </span>
                                </td>
                                <td class="whitespace-nowrap text-sm py-2 px-2 font-medium text-gray-900">
                                    {{ service?.contractCode }}
                                </td>

                                <td class="whitespace-nowrap text-sm py-2 px-2 text-gray-500">
                                    {{ service?.contractName }}
                                </td>

                                <td class="whitespace-nowrap text-sm py-2 px-2 font-medium">
                                    <div class="flex w-full">
                                        <span class="px-3 py-0.5 rounded-full w-full text-center" :class="service?.status == 'ACTIVE'
                                            ? 'bg-green-100 text-green-600'
                                            : 'bg-red-100 text-red-600'
                                            ">
                                            {{
                                                service?.status == 'ACTIVE' ? 'Active' : 'Closed'
                                            }}
                                        </span>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 px-2 font-semibold underline underline-offset-4">
                                    <button @click="
                                        router.push({
                                            name: 'assign-eligible-service',
                                            params: {
                                                id: service?.payerInstitutionContractUuid,
                                                Uuid: service?.institutionUuid
                                            },
                                        })
                                        " title="Product Coverages"
                                        class="bg-primary p-1 mx-2 rounded-lg hover:bg-primary hover:shadow-xl duration-200">
                                        <Icon icon="iconoir:healthcare" class="text-white text-2xl" />
                                    </button>
                                    <button @click="
                                        router.push({
                                            name: 'assign-insured-person',
                                            params: {
                                                id: service?.payerInstitutionContractUuid,
                                                Uuid: service?.institutionUuid
                                            },
                                        })
                                        " title="Insured Persons"
                                        class="bg-primary p-1 rounded-lg hover:bg-primary hover:shadow-xl duration-200">
                                        <Icon icon="mdi:users-check-outline" class="text-white text-2xl" />
                                    </button>
                                    <button title="Add Providers"
                                        class="bg-primary p-1 mx-2 rounded-lg hover:bg-primary hover:shadow-xl duration-200"
                                        @click="router.push({
                                            name: 'ProvidersInContract',
                                            params: {
                                                Uuid: service?.payerInstitutionContractUuid,
                                                id: service?.institutionUuid
                                            },
                                        })">
                                        <div class="flex flex-row">
                                            <span></span>
                                            <Icon icon="mdi:hospital-building" class="text-white text-2xl" />
                                        </div>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <loader />
                        </tbody>
                    </table>
                    <p v-if="contracts && contracts.length === 0"
                        class="min-w-full divide-y divide-gray-300 mt-4 items-center mb-4 px-80">
                        No Items Were Found
                    </p>
                    <div class="col-span-full">
                        <label for="Description"
                            class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                        <div class="mt-1">
                            <textarea rows="4" v-model="description" name="description" id="description"
                                class="block w-full h-20 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end my-4 gap-x-6 border-gray-900/10 px-4 sm:px-8">
                    <button @click="SaveContract" type="submit"
                        class="rounded-md w-80 bg-indigo-600 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Save
                    </button>
                    <!-- <button
                        class="flex items-center text-lg px-4 py-1 font-semibold text-white bg-primary0 rounded-lg hover:shadow-lg duration-200 hover:bg-primary"
                        @click="goBack">
                        <Icon icon="lucide:check" class="text-2xl" />
                        Back
                    </button> -->
                </div>
            </div>
        </SectionMain>
    </div>
</template>